package com.knet.common.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/20 11:18
 * @description: 订单服务常量
 */
@Getter
public class OrderServicesConstants {

    /**
     * 订单服务消息前缀
     */
    public static final String KNET_B2B_ORDER_MESSAGE_PREFIX = "knet:b2b:order:message:%s";

    /**
     * 订单列表缓存名称
     */
    public static final String ORDER_LIST_CACHE_NAME = "orderList";
    /**
     * 管理员订单列表缓存名称
     */
    public static final String ADMIN_ORDER_LIST_CACHE_NAME = "adminOrderList";

    /**
     * 订单列表缓存过期时间 30秒
     */
    public static final Long ORDER_LIST_CACHE_EXPIRED_TIME = 30L;

    /**
     * mq 订单创建队列
     */
    public static final String ORDER_CREATED = "order.created";
    /**
     * mq 订单退款队列
     */
    public static final String ORDER_REFUND = "order.refund";
    /**
     * mq 订单超时队列
     */
    public static final String ORDER_TIMEOUT = "order.timeout";
    /**
     * mq 订单交换机
     */
    public static final String ORDER_EXCHANGE = "order-exchange";
    /**
     * 订单未支付超时取消时间 5分钟
     */
    public static final Integer ORDER_TIMEOUT_TIME = 5 * 60 * 1000;

    /**
     * 订单支付后 10分钟 不能取消
     */
    public static final Integer ORDER_PAID_TIMEOUT_TIME = 10;
    /**
     * 订单完成检查任务每次处理的订单数量
     */
    public static final int ORDER_COMPLETED_BATCH_SIZE = 200;
}
