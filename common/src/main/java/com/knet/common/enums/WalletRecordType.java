package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/16 18:00
 * @description: 钱包交易类型枚举
 */
@Getter
@AllArgsConstructor
public enum WalletRecordType {
    /**
     * 支付扣款
     */
    PAYMENT_DEDUCTION(1, "PAYMENT_DEDUCTION", "支付扣款"),
    /**
     * 退款入账
     */
    REFUND_INCOME(2, "REFUND_INCOME", "退款入账"),
    /**
     * 充值
     */
    RECHARGE(3, "RECHARGE", "充值"),
    /**
     * 提现
     */
    WITHDRAW(4, "WITHDRAW", "提现"),
    /**
     * 管理员扣款
     */
    DEDUCT(5, "DEDUCT", "管理员扣款"),
    /**
     * 补偿
     */
    COMPENSATION(6, "COMPENSATION", "补偿");

    /**
     * 交易类型代码
     */
    private final Integer code;

    /**
     * 交易类型名称，用于数据库存储
     */
    @EnumValue
    @JsonValue
    private final String name;

    /**
     * 描述
     */
    private final String desc;

    public static WalletRecordType fromCode(int code) {
        for (WalletRecordType walletRecordType : WalletRecordType.values()) {
            if (walletRecordType.getCode() == code) {
                return walletRecordType;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }
}
