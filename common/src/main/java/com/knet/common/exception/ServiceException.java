package com.knet.common.exception;

import lombok.Getter;

import java.io.Serial;

/**
 * <AUTHOR>
 * @date 2025/2/13 15:52
 * @description: 服务统一异常
 */
@Getter
public class ServiceException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private final int statusCode;

    public ServiceException(String message) {
        super(message);
        this.statusCode = 500;
    }

    public ServiceException(String message, Throwable cause) {
        super(message, cause);
        this.statusCode = 500;
    }

    /**
     * 自定义状态码
     *
     * @param message    异常信息
     * @param statusCode 状态码
     */
    public ServiceException(String message, int statusCode) {
        super(message);
        this.statusCode = statusCode;
    }
}
