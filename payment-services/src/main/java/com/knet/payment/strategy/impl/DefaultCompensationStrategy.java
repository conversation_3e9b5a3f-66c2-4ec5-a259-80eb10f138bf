package com.knet.payment.strategy.impl;

import com.knet.payment.model.dto.req.RefundPaymentRequest;
import com.knet.payment.strategy.CompensationStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/8/27 10:44
 * @description: 默认补偿策略实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultCompensationStrategy implements CompensationStrategy {

    @Override
    public BigDecimal calculateCompensation(RefundPaymentRequest request) {
        //补偿金额默认2块
        BigDecimal bigDecimal = Boolean.TRUE.equals(request.getIsCompensation()) ? BigDecimal.valueOf(2) : BigDecimal.ZERO;
        log.info("子订单no: {},计算补偿金额: {}", request.getOrderItemNo(), bigDecimal);
        return bigDecimal;
    }
}
