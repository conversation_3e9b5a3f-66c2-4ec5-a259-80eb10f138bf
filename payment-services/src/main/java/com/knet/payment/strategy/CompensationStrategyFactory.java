package com.knet.payment.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/8/27 11:05
 * @description: 补偿策略工厂
 */
@Slf4j
@Component
public class CompensationStrategyFactory {
    private final Map<String, CompensationStrategy> strategies;
    private final List<String> strategyNames;

    @Autowired
    public CompensationStrategyFactory(Map<String, CompensationStrategy> strategyMap) {
        this.strategies = new ConcurrentHashMap<>(strategyMap);
        this.strategyNames = new ArrayList<>(strategyMap.keySet());
        logStrategies(); // 初始化时立即记录日志
    }

    private void logStrategies() {
        if (strategies.isEmpty()) {
            log.warn("⚠️ 补偿策略工厂未加载任何策略实现");
            return;
        }
        log.info("✅ 补偿策略工厂已加载 {} 个策略:", strategies.size());
        strategies.forEach((beanName, strategy) ->
                log.info("▸ 策略名称: [{}] → 实现类: {}",
                        beanName, strategy.getClass().getSimpleName())
        );
    }

    public CompensationStrategy getStrategy(String strategyName) {
        CompensationStrategy strategy = strategies.get(strategyName);
        if (strategy == null) {
            String errorMsg = String.format("未找到补偿策略: %s。可用策略: %s",
                    strategyName, String.join(", ", strategyNames));
            log.error(errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }
        return strategy;
    }

    public CompensationStrategy getDefaultStrategy() {
        final String DEFAULT_STRATEGY_NAME = "defaultCompensationStrategy";
        log.debug("正在获取默认补偿策略: {}", DEFAULT_STRATEGY_NAME);
        return getStrategy(DEFAULT_STRATEGY_NAME);
    }
}
