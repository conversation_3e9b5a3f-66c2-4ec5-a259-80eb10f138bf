package com.knet.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.payment.model.entity.SysRefundRecord;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【sys_refund_record(退款记录表)】的数据库操作Service
 * @date 2025-03-12 15:28:58
 */
public interface ISysRefundRecordService extends IService<SysRefundRecord> {

    /**
     * 根据订单项ID查询退款记录
     *
     * @param orderItemId 订单项ID
     * @return 退款记录列表
     */
    List<SysRefundRecord> getRefundRecordsByOrderItemId(Long orderItemId);

    /**
     * 计算订单项的累计退款金额
     *
     * @param orderItemId 订单项ID
     * @return 累计退款金额
     */
    BigDecimal getTotalRefundAmountByOrderItemId(Long orderItemId);

    /**
     * 检查订单项是否已经退款
     *
     * @param orderItemId 订单项ID
     * @return 是否已退款
     */
    boolean isOrderItemRefunded(Long orderItemId);
}
