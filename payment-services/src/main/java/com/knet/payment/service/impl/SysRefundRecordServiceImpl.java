package com.knet.payment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.payment.mapper.SysRefundRecordMapper;
import com.knet.payment.model.entity.SysRefundRecord;
import com.knet.payment.service.ISysRefundRecordService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【sys_refund_record(退款记录表)】的数据库操作Service实现
 * @date 2025-03-12 15:28:58
 */
@Service
public class SysRefundRecordServiceImpl extends ServiceImpl<SysRefundRecordMapper, SysRefundRecord>
        implements ISysRefundRecordService {

    @Override
    public List<SysRefundRecord> getRefundRecordsByOrderItemId(Long orderItemId) {
        LambdaQueryWrapper<SysRefundRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRefundRecord::getItemId, String.valueOf(orderItemId))
                .eq(SysRefundRecord::getStatus, 1);
        return this.list(wrapper);
    }

    @Override
    public BigDecimal getTotalRefundAmountByOrderItemId(Long orderItemId) {
        List<SysRefundRecord> refundRecords = getRefundRecordsByOrderItemId(orderItemId);
        return refundRecords.stream()
                .map(SysRefundRecord::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public boolean isOrderItemRefunded(Long orderItemId) {
        LambdaQueryWrapper<SysRefundRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRefundRecord::getItemId, String.valueOf(orderItemId))
                .eq(SysRefundRecord::getStatus, 1);
        return this.count(wrapper) > 0;
    }
}
