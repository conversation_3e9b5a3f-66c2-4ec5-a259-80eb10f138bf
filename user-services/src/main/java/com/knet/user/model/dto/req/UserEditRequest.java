package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.enums.LanguageEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/2/14 17:56
 * @description: 用户请求体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserEditRequest extends BaseRequest {

    @NotNull(message = "用户ID 不能为空")
    @Schema(description = "id,用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String password;

    @Schema(description = "原始密码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String orgPassword;

    /**
     * @see LanguageEnum 语言
     */
    @Schema(description = "语言,默认英语")
    private LanguageEnum language = LanguageEnum.EN_US;

    @Schema(description = "角色id,默认是商家用户")
    private Long roleId = 2L;
}
