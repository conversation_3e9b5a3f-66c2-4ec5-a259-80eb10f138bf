# 订单商品替换接口实现方案

## 1. 需求概述

### 1.1 功能描述

为第三方系统提供订单商品超卖替换接口，支持根据订单项编号 `itemNo` 和新的 `oneId`，替换订单中对应商品的 `oneId` 和
`listingId`。

### 1.2 业务流程

1. 第三方传入订单项编号 `itemNo` 和新的 `oneId`
2. 系统检查新 `oneId` 是否在商品系统中上架
3. **智能处理逻辑**：
    - **如果商品已上架**：直接获取listingId，将商品状态更新为LOCKED，标记为超卖替换类型
    - **如果商品未上架**：复制原商品所有属性，仅修改oneId和listingId，创建新商品并设置为LOCKED状态和超卖替换标记
4. 更新订单项的 `oneId` 和 `knetListingId`
5. 返回操作结果

## 2. 技术架构设计

### 2.1 模块选择

- **实现模块**: `order-services`
- **支持模块**: `goods-services`
- **原因**: 主要涉及订单数据修改，符合业务边界划分，商品服务提供内部支持

### 2.2 架构图

```mermaid
sequenceDiagram
    participant Third as 第三方系统
    participant Order as order-services
    participant Goods as goods-services
    participant DB as 数据库

    Third->>Order: POST /api/order/replace-product
    Order->>Order: 查询订单项信息
    Order->>Goods: 检查新oneId是否上架
    alt 商品已上架
        Order->>Goods: 获取listingId
        Order->>Goods: 锁定商品并标记为超卖替换
        Goods->>DB: 更新商品状态为LOCKED和超卖替换标记
        Goods-->>Order: 返回操作结果
    else 商品未上架
        Order->>Goods: 基于原商品创建新商品
        Goods->>DB: 复制商品数据(LOCKED状态+超卖替换标记)
        Goods-->>Order: 返回新listingId
    end
    Order->>DB: 更新订单项信息
    Order-->>Third: 返回操作结果
```

## 3. 接口设计

### 3.1 接口定义

```java

@ModifyHeader(handlerType = "API_KEY")
@Loggable(value = "替换订单商品")
@PostMapping("/api/order/replace-product")
@Operation(summary = "替换订单商品信息", description = "第三方传参数订单itemNo和新oneId替换订单对应的oneId与listingId")
public HttpResult<ReplaceProductResp> replaceOrderProduct(
        @Validated @RequestBody ReplaceProductRequest request)
```

### 3.2 请求参数

```java
@Data
public class ReplaceProductRequest {
    @NotBlank(message = "订单项编号不能为空")
    private String itemNo;

    @NotBlank(message = "新的oneId不能为空")  
    private String newOneId;
}
```

### 3.3 响应参数

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReplaceProductResp {
    private String itemNo;
    private String oldOneId;
    private String oldListingId;
    private String newOneId;
    private String newListingId;
    private Boolean success;
    private String message;  // "超卖替换成功"
}
```

## 4. 核心实现

### 4.1 超卖替换标识

在 `ProductMark` 枚举中新增：

```java
/**
 * 超卖替换
 */
OVERSELL_REPLACEMENT(5, "OVERSELL_REPLACEMENT")
```

### 4.2 智能处理逻辑实现

```java
@Loggable(value = "替换订单商品")
@DistributedLock(key = "'replace:product:' + #request.itemNo", expire = 30)
@Transactional(rollbackFor = Exception.class)
public ReplaceProductResp replaceOrderProduct(ReplaceProductRequest request) {
    // 1. 查询订单项信息
    SysOrderItem orderItem = getOrderItemByItemNo(request.getItemNo());
    
    // 2. 检查新oneId在商品系统中是否上架
    HttpResult<Boolean> onSaleResult = goodsServiceClient.checkProductOnSale(request.getNewOneId());
    
    String newListingId;
    if (Boolean.TRUE.equals(onSaleResult.getData())) {
        // 3a. 商品已上架：获取listingId并锁定商品，标记为超卖替换
        HttpResult<String> listingResult = goodsServiceClient.getListingIdByOneId(request.getNewOneId());
        newListingId = listingResult.getData();
        
        // 锁定商品并标记为超卖替换 (status->LOCKED, mark->OVERSELL_REPLACEMENT)
        goodsServiceClient.lockProductForOversellReplacement(request.getNewOneId());
    } else {
        // 3b. 商品未上架：基于原商品创建新商品（自动设置LOCKED状态和超卖替换标记）
        newListingId = createNewProductBasedOnOld(oldOneId, request.getNewOneId());
    }
    
    // 4. 更新订单项信息
    updateOrderItem(request.getItemNo(), request.getNewOneId(), newListingId);
    
    return ReplaceProductResp.builder()
            .message("超卖替换成功")
            .build();
}
```

## 5. 商品服务支持实现

### 5.1 内部接口

```java

@RestController
@RequestMapping("/inner")
public class InnerProductController {

    @PostMapping("/check-on-sale")
    public HttpResult<Boolean> checkProductOnSale(@RequestParam String oneId);

    @PostMapping("/get-listing-id")
    public HttpResult<String> getListingIdByOneId(@RequestParam String oneId);

    @PostMapping("/create-based-on-existing")
    public HttpResult<String> createProductBasedOnExisting(@RequestBody CreateProductBasedOnExistingRequest request);

    @PostMapping("/lock-for-oversell-replacement")
    public HttpResult<Void> lockProductForOversellReplacement(@RequestParam String oneId);
}
```

### 5.2 商品锁定逻辑

```java
@Transactional(rollbackFor = Exception.class)
public void lockProductForOversellReplacement(String oneId) {
    LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
    updateWrapper
            .eq(KnetProduct::getOneId, oneId)
            .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
            .set(KnetProduct::getStatus, ProductStatus.LOCKED)
            .set(KnetProduct::getMark, ProductMark.OVERSELL_REPLACEMENT);
    
    int updatedCount = baseMapper.update(null, updateWrapper);
}
```

### 5.3 新商品创建逻辑

```java
@Transactional(rollbackFor = Exception.class)
public String createProductBasedOnExisting(CreateProductBasedOnExistingRequest request) {
    // 1. 查询源商品信息
    KnetProduct sourceProduct = getProductByOneId(request.getSourceOneId());
    
    // 2. 复制商品信息，修改oneId和listingId，设置为超卖替换状态
    KnetProduct newProduct = BeanUtil.copyProperties(sourceProduct, KnetProduct.class);
    newProduct.setId(null);
    newProduct.setOneId(request.getTargetOneId());
    newProduct.setListingId(randomStrUtil.getProductId());
    newProduct.setStatus(ProductStatus.LOCKED); // 设置为锁定状态
    newProduct.setMark(ProductMark.OVERSELL_REPLACEMENT); // 标记为超卖替换
    newProduct.setCreateTime(new Date());
    newProduct.setUpdateTime(new Date());
    
    // 3. 保存新商品
    baseMapper.insert(newProduct);
    
    return newProduct.getListingId();
}
```

## 6. 跨服务通信

### 6.1 Feign客户端

```java
@FeignClient(name = "goods-services", path = "/inner")
public interface GoodsServiceClient {
    
    @PostMapping("/check-on-sale")
    HttpResult<Boolean> checkProductOnSale(@RequestParam("oneId") String oneId);
    
    @PostMapping("/get-listing-id")
    HttpResult<String> getListingIdByOneId(@RequestParam("oneId") String oneId);
    
    @PostMapping("/create-based-on-existing")
    HttpResult<String> createProductBasedOnExisting(@RequestBody CreateProductBasedOnExistingRequest request);
    
    @PostMapping("/lock-for-oversell-replacement")
    HttpResult<Void> lockProductForOversellReplacement(@RequestParam("oneId") String oneId);
}
```

## 7. 数据库设计

### 7.1 订单项表字段

确保 `sys_order_item` 表有以下字段：

- `one_id`: VARCHAR(64) - 商品oneId
- `knet_listing_id`: VARCHAR(64) - 商品listingId

### 7.2 数据更新SQL

```java
@Update("UPDATE sys_order_item SET one_id = #{oneId}, knet_listing_id = #{listingId}, " +
        "update_time = NOW() WHERE item_no = #{itemNo}")
int updateProductInfo(@Param("itemNo") String itemNo,
                     @Param("oneId") String oneId, 
                     @Param("listingId") String listingId);
```

## 8. 技术保障

### 8.1 并发控制

- **分布式锁**: `@DistributedLock(key = "'replace:product:' + #request.itemNo", expire = 30)`
- **防止同一订单项的并发修改**

### 8.2 事务控制

- **订单服务**: 本地事务 `@Transactional(rollbackFor = Exception.class)`
- **商品服务**: 通过Feign调用，失败时会回滚订单事务

### 8.3 权限控制

- **API_KEY验证**: `@ModifyHeader(handlerType = "API_KEY")`
- **接口访问控制**: 仅允许授权的第三方系统调用

### 8.4 日志记录

```java
@Loggable(value = "替换订单商品")
log.info("开始替换订单商品: itemNo={}, newOneId={}", itemNo, newOneId);
log.info("商品已上架，获取到listingId: {}，已锁定并标记为超卖替换", newListingId);
log.info("商品未上架，已创建新商品，newListingId: {}，状态为LOCKED，标记为超卖替换", newListingId);
```

## 9. 业务流程总结

### 9.1 智能处理策略

1. **商品已上架场景**:
    - 直接获取现有商品的listingId
    - 将商品状态从ON_SALE变更为LOCKED
    - 设置商品标记为OVERSELL_REPLACEMENT

2. **商品未上架场景**:
    - 复制原商品的所有属性信息
    - 仅修改oneId和listingId为新值
    - 自动设置新商品状态为LOCKED
    - 自动设置新商品标记为OVERSELL_REPLACEMENT

### 9.2 状态流转

```
原订单项: oneId_old + knetListingId_old
    ↓
智能处理(检查新oneId是否上架)
    ↓
情况A: 已上架 → 锁定现有商品(LOCKED + OVERSELL_REPLACEMENT)
情况B: 未上架 → 创建新商品(LOCKED + OVERSELL_REPLACEMENT)
    ↓
更新订单项: oneId_new + knetListingId_new
    ↓
返回替换结果
```

## 10. 接口使用示例

### 10.1 请求示例

```bash
POST /api/order/replace-product
Content-Type: application/json
API-KEY: your-api-key

{
    "itemNo": "ORDER_ITEM_001",
    "newOneId": "NEW_ONE_ID_123"
}
```

### 10.2 响应示例

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "itemNo": "ORDER_ITEM_001",
        "oldOneId": "OLD_ONE_ID_456",
        "oldListingId": "OLD_LISTING_ID_789",
        "newOneId": "NEW_ONE_ID_123",
        "newListingId": "NEW_LISTING_ID_ABC",
        "success": true,
        "message": "超卖替换成功"
    }
}
```

## 11. 运维监控

### 11.1 关键指标

- 替换成功率
- 商品锁定成功率
- 新商品创建成功率
- 接口响应时间

### 11.2 异常处理

- 订单项不存在
- 商品服务调用失败
- 数据库更新失败
- 并发冲突处理

## 12. 总结

超卖替换功能已完全实现，具备以下特点：

1. **智能判断**: 自动识别商品是否上架，采用不同的处理策略
2. **状态管理**: 统一将替换商品设置为LOCKED状态，避免被其他操作影响
3. **标识追踪**: 通过OVERSELL_REPLACEMENT标记可以清晰识别哪些商品是超卖替换产生的
4. **完整性**: 两种情况下都能确保订单项成功替换为新的商品信息
5. **安全性**: 分布式锁、权限控制、事务保证等多重保障
6. **可追踪**: 完善的日志记录，便于问题排查和业务分析

该方案满足了第三方系统的超卖替换需求，在保证数据一致性的同时，提供了灵活的商品替换策略。
