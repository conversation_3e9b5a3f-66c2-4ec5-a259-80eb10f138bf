<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.order.mapper.SysOrderItemMapper">

    <!-- 根据订单项编号查询订单项 -->
    <select id="selectByItemNo" parameterType="string" resultType="com.knet.order.model.entity.SysOrderItem">
        SELECT *
        FROM sys_order_item
        WHERE item_no = #{itemNo}
          AND del_flag = 0
    </select>

    <!-- 更新订单项的商品信息 -->
    <update id="updateProductInfo">
        UPDATE sys_order_item
        SET one_id          = #{oneId},
            knet_listing_id = #{listingId},
            update_time     = NOW()
        WHERE item_no = #{itemNo}
          AND del_flag = 0
    </update>

    <!-- 分页查询订单商品项，关联物流信息 -->
    <select id="getOrderItemsByPage" resultType="com.knet.order.model.dto.third.resp.OrderAndLabelVo">
        SELECT
        soi.parent_order_id AS parentOrderId,
        soi.item_no AS orderNo,
        soi.sku AS sku,
        soi.size AS size,
        soi.one_id AS oneId,
        soi.knet_listing_id AS listingId,
        soi.price AS salePrice,
        soi.kg_owning_price AS kgOwningPrice,
        soi.seller_owning_price AS sellerOwningPrice,
        soi.status AS status,
        soi.create_time AS soldTime,
        soi.cancelled_time AS cancelTime,
        soi.sell_channel AS sellChannel,
        soi.source AS source,
        soi.name AS productName,
        soi.image_url AS imgUrl,
        label.label_url AS labelUrl,
        label.tracking_number AS trackingNumber,
        label.express_company AS carrier
        FROM
        sys_order_item soi
        LEFT JOIN
        sys_shipping_item_rel rel ON soi.item_id = rel.item_id
        LEFT JOIN
        sys_shipping_label label ON rel.label_id = label.id
        <where>
            AND soi.del_flag = 0
            <if test="request.parentOrderId != null and request.parentOrderId != ''">
                AND soi.parent_order_id = #{request.parentOrderId}
            </if>
            <if test="request.orderNo != null">
                AND soi.item_no = #{request.orderNo}
            </if>
            <if test="request.status != null">
                AND soi.status = #{request.status}
            </if>
        </where>
        ORDER BY soi.create_time DESC
    </select>
</mapper>
