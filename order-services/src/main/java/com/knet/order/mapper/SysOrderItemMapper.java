package com.knet.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.order.model.dto.third.req.KnetB2bOrderQueryRequest;
import com.knet.order.model.dto.third.resp.OrderAndLabelVo;
import com.knet.order.model.entity.SysOrderItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order_item(订单商品明细)】的数据库操作Mapper
 * * @Entity com.knet.order.model/entity.SysOrderItem
 */
@Mapper
public interface SysOrderItemMapper extends BaseMapper<SysOrderItem> {

    /**
     * 分页查询订单项，关联物流信息
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 订单项分页结果
     */
    IPage<OrderAndLabelVo> getOrderItemsByPage(
            Page<OrderAndLabelVo> page, @Param("request") KnetB2bOrderQueryRequest request);

    /**
     * 根据订单项编号查询订单项
     *
     * @param itemNo 订单项编号
     * @return 订单项信息
     */
    SysOrderItem selectByItemNo(@Param("itemNo") String itemNo);

    /**
     * 更新订单项的商品信息
     *
     * @param itemNo    订单项编号
     * @param oneId     新的oneId
     * @param listingId 新的listingId
     * @return 更新行数
     */
    int updateProductInfo(@Param("itemNo") String itemNo,
                          @Param("oneId") String oneId,
                          @Param("listingId") String listingId);
}
