package com.knet.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.vo.OrderItemDataVo;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order_item(订单商品明细)】的数据库操作Service
 */
public interface ISysOrderItemService extends IService<SysOrderItem> {

    /**
     * 创建订单明细记录（每条记录数量为1，相同商品创建多条记录）
     *
     * @param subOrderId        子订单ID
     * @param orderItemDataList 订单商品数据
     * @param parentOrderId     父订单ID
     * @return 订单明细列表
     */
    List<SysOrderItem> createOrderItems(String subOrderId, List<OrderItemDataVo> orderItemDataList, String parentOrderId);

    /**
     * 根据父订单ID获取订单明细
     *
     * @param prentOrderId 父订单ID
     * @return 订单明细列表
     */
    List<SysOrderItem> getOrderItemsByPrentOrderId(String prentOrderId);

    /**
     * 根据父订单ID更新订单明细状态
     *
     * @param parentOrderId 父订单ID
     * @param status        新状态
     * @return 是否更新成功
     */
    boolean updateOrderStatusByParentId(String parentOrderId, KnetOrderItemStatus status);

    /**
     * 根据订单项ID列表更新订单明细状态
     *
     * @param itemIds 订单项ID列表
     * @param status  新状态
     * @return 是否更新成功
     */
    boolean updateOrderStatusByItemIds(List<Long> itemIds, KnetOrderItemStatus status);

    /**
     * 根据订单项编号查询订单项
     *
     * @param itemNo 订单项编号
     * @return SysOrderItem
     */
    SysOrderItem getOrderItemByItemNo(String itemNo);

    /**
     * 更新订单项的商品信息
     *
     * @param itemNo       订单项编号
     * @param newOneId     新的oneId
     * @param newListingId 新的listingId
     */
    void updateOrderItem(String itemNo, String newOneId, String newListingId);
}
