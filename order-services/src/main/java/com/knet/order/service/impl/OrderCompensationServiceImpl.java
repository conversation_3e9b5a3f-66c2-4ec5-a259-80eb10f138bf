package com.knet.order.service.impl;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.OrderMessage;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.service.IOrderCompensationService;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/13 14:28
 * @description: 订单超时补偿机制 实现
 */
@Slf4j
@Service
public class OrderCompensationServiceImpl implements IOrderCompensationService {
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private ISysOrderProcessService orderProcessService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processOrderTimeout(String messageBody) {
        log.info("订单服务处理订单超时: {}", messageBody);
        OrderMessage message = JSON.parseObject(messageBody, OrderMessage.class);
        try {
            String orderId = message.getOrderId();
            //获取订单状态，处于待支付的更新为取消订单
            SysOrderGroup orderGroup = orderGroupService.getOrderGroupByOrderId(orderId);
            if (!KnetOrderGroupStatus.PENDING_PAYMENT.equals(orderGroup.getStatus())) {
                log.info("订单状态不是待支付，不做超时处理: orderId={}, status={}", orderId, orderGroup.getStatus());
                return;
            }
            orderProcessService.systemCancelOrder(orderId);
            //清除订单缓存
            clearUserOrderListCache(message.getUserId());
        } catch (Exception e) {
            log.error("处理订单超时异常: orderId={}, error={}", message.getOrderId(), e.getMessage());
            throw new ServiceException("处理订单超时异常: " + e.getMessage());
        }
    }

    /**
     * 清除用户订单列表缓存
     * 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
     */
    private void clearUserOrderListCache(Long userId) {
        try {
            // 构建缓存key模式，匹配该用户的所有订单列表缓存
            String cacheKeyPattern = "order-service:orderList:" + userId + ":*";
            // 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
            RedisCacheUtil.deleteByPattern(cacheKeyPattern);
            log.info("已清除用户订单列表缓存，用户ID: {}, 缓存key模式: {}", userId, cacheKeyPattern);
        } catch (Exception e) {
            log.warn("清除用户订单列表缓存失败，用户ID: {}, 错误: {}", userId, e.getMessage());
        }
    }
}
