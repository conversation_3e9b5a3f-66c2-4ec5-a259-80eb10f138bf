package com.knet.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.order.model.dto.req.ReplaceProductRequest;
import com.knet.order.model.dto.resp.ReplaceProductResp;
import com.knet.order.model.dto.third.req.KnetB2bOrderQueryRequest;
import com.knet.order.model.dto.third.req.UpdatedOrderRequest;
import com.knet.order.model.dto.third.resp.KnetB2bOrderQueryVo;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.vo.SubOrderGroupVo;
import com.knet.order.model.vo.SubOrderItemVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:28
 * @description: 订单对外提供服务定义接口
 */
public interface IApiOrdersService {
    /**
     * 根据父订单ID获取订单明细
     *
     * @param prentOrderId 父订单ID
     * @return 订单明细列表
     */
    List<SubOrderItemVo> getOrderItemsByPrentOrderId(String prentOrderId);

    /**
     * 根据父订单ID获取订单信息
     *
     * @param prentOrderId 父订单ID
     * @return 订单信息
     */
    SubOrderGroupVo getOrderGroupByPrentOrderId(String prentOrderId);

    /**
     * 分页查询订单明细
     *
     * @param request 查询条件
     * @return 订单明细列表
     */
    IPage<KnetB2bOrderQueryVo> getOrderItemsByPage(KnetB2bOrderQueryRequest request);

    /**
     * 更新订单状态
     *
     * @param request request
     * @return 更新结果
     */
    boolean updateOrderStatus(UpdatedOrderRequest request);

    /**
     * 根据订单项ID获取订单项详情
     *
     * @param itemId 订单项ID
     * @return 订单项详情
     */
    SysOrderItem getOrderItemDetails(String itemId);

    /**
     * 根据父订单号获取订单item详情列表
     *
     * @param prentOrderId 父订单号
     * @return 订单项详情
     */
    List<SysOrderItem> queryOrderItemsByPrentOrderId(String prentOrderId);

    /**
     * 替换订单商品信息
     *
     * @param request 替换请求
     * @return 替换结果
     */
    ReplaceProductResp replaceOrderProduct(ReplaceProductRequest request);

    /**
     * 检查oneId冲突
     *
     * @param oneIds 要检查的oneId列表
     * @param excludeOrderId 排除的订单ID
     * @return 冲突的oneId列表
     */
    List<String> checkOneIdConflicts(List<String> oneIds, String excludeOrderId);
}
