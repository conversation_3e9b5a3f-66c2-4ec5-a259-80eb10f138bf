package com.knet.order.model.dto.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 商品上架状态查询响应
 *
 * <AUTHOR>
 * @date 2025/09/02
 * @description: 超卖替换功能中用于一次性获取商品上架状态和listingId信息的响应对象
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductOnSaleInfoResp extends BaseResponse {
    @Schema(description = "商品是否上架")
    private Boolean onSale;
    @Schema(description = "商品listingId")
    private String listingId;
}
