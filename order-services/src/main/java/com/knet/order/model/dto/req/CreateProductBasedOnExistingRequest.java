package com.knet.order.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 基于现有商品创建新商品请求
 *
 * <AUTHOR>
 * @date 2025/09/02
 * @description: 超卖替换功能中用于基于原商品信息创建新商品的请求参数，包含源商品和目标商品的oneId
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CreateProductBasedOnExistingRequest extends BaseRequest {
    @Schema(description = "源商品oneId", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sourceOneId;
    @Schema(description = "目标商品oneId", requiredMode = Schema.RequiredMode.REQUIRED)
    private String targetOneId;
}
