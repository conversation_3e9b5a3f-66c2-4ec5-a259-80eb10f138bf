package com.knet.order.model.vo;

import com.knet.order.model.entity.SysOrder;
import com.knet.order.model.entity.SysOrderItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4 14:49
 * @description: SubOrderDataDto
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SubOrderDataVo {
    @Schema(description = "子订单")
    private SysOrder subOrder;
    @Schema(description = "订单明细项列表")
    private List<SysOrderItem> orderItems;
}
