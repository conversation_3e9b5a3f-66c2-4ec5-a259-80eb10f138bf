package com.knet.order.model.dto.req;

import com.knet.common.base.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 替换订单商品请求
 *
 * <AUTHOR>
 * @date 2025/9/02
 * @description: 超卖替换功能的请求参数，用于第三方系统传入订单项编号和新商品ID来替换订单中的商品信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReplaceProductRequest extends BaseRequest {

    @NotBlank(message = "订单项编号不能为空")
    private String itemNo;

    @NotBlank(message = "新的oneId不能为空")
    private String newOneId;
}
