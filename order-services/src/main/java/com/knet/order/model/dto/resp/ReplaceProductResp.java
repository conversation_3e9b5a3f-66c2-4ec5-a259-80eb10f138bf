package com.knet.order.model.dto.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;


/**
 * 替换订单商品响应
 *
 * <AUTHOR>
 * @date 2025/09/02
 * @description: 超卖替换功能的响应结果，包含替换前后的商品信息和操作结果状态
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReplaceProductResp extends BaseResponse {

    @Schema(description = "订单项编号")
    private String itemNo;

    @Schema(description = "原商品ID")
    private String oldOneId;

    @Schema(description = "原商品列表ID")
    private String oldListingId;

    @Schema(description = "新商品ID")
    private String newOneId;

    @Schema(description = "新商品列表ID")
    private String newListingId;

    @Schema(description = "替换是否成功")
    private Boolean success;

    @Schema(description = "响应消息")
    private String message;
}
