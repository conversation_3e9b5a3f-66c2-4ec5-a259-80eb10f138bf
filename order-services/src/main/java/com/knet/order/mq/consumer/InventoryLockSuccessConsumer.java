package com.knet.order.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.InventoryLockSuccessMessage;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.service.IInventoryConsumerService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.SystemConstant.ORDER_INVENTORY_LOCK_SUCCESS_PROCESSED;
import static com.knet.common.constants.SystemConstant.PROCESSED;

/**
 * <AUTHOR>
 * @date 2025/12/19 16:30
 * @description: 库存锁定成功消息消费者
 */
@Slf4j
@Component
public class InventoryLockSuccessConsumer {

    @Resource
    private IInventoryConsumerService inventoryConsumerService;

    @RabbitListener(
            queues = "inventory-lock-success-queue.order-services",
            ackMode = "MANUAL"
    )
    public void handleInventoryLockSuccess(
            @Payload String messageBody,
            @Header("routingKey") String routingKey,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if ("inventory.lock.success".equals(routingKey)) {
                InventoryLockSuccessMessage lockSuccessMessage = JSON.parseObject(messageBody, InventoryLockSuccessMessage.class);
                String orderId = lockSuccessMessage.getOrderId();
                // 使用messageId进行幂等性检查，确保同一条消息只处理一次
                String messageIdempotencyKey = ORDER_INVENTORY_LOCK_SUCCESS_PROCESSED.formatted(messageId);
                if (!RedisCacheUtil.setIfAbsent(messageIdempotencyKey, PROCESSED, 1800)) {
                    log.warn("订单服务库存锁定成功重复处理: orderId={}, messageId={}", orderId, messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                try {
                    log.info("订单服务处理库存锁定成功消息: orderId={}, messageId={}", orderId, messageId);
                    inventoryConsumerService.processInventoryLockSuccess(lockSuccessMessage);
                    channel.basicAck(deliveryTag, false);
                    log.info("订单服务库存锁定成功处理完成: orderId={}, messageId={}", orderId, messageId);
                } catch (Exception e) {
                    // 处理失败时不清理幂等性标记，避免重复消费
                    // 只有在业务逻辑确实需要重试时才清理标记
                    log.error("库存锁定成功处理失败，保持幂等性标记: orderId={}, messageId={}, error={}", orderId, messageId, e.getMessage());
                    throw e;
                }
            }
        } catch (Exception e) {
            log.error("库存锁定成功处理失败: {}", messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        }
    }
}
