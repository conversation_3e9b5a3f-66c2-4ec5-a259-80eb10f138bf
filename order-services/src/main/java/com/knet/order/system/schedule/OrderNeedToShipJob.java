package com.knet.order.system.schedule;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.service.ISysOrderProcessService;
import com.knet.order.system.event.OrderFrozenEvent;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.knet.common.constants.OrderServicesConstants.ORDER_PAID_TIMEOUT_TIME;

/**
 * <AUTHOR>
 * @date 2025/6/13 14:47
 * @description: 订单待发货定时任务
 */
@Slf4j
@Component
public class OrderNeedToShipJob {
    @Resource
    private ISysOrderItemService orderItemService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private ISysOrderProcessService orderProcessService;

    /**
     * 每5分钟执行一次，订单已经支付超过10分钟后，转换为FROZEN 冻结
     */
    @XxlJob("needToShipOrder")
    public ReturnT<String> needToShipOrder() {
        log.info("任务 needToShipOrder 开始执行 ");
        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        XxlJobHelper.log("XXL-JOB 任务触发时间: {}", time);
        StopWatch stopWatch = new StopWatch("needToShipOrder");
        stopWatch.start();
        // 查询条件：已支付状态的订单
        LambdaQueryWrapper<SysOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrderItem::getStatus, KnetOrderItemStatus.PAID);
        // 计算订单支付超时时间点
        LocalDateTime timeoutThreshold = LocalDateTime.now().minus(Duration.ofMinutes(ORDER_PAID_TIMEOUT_TIME));
        queryWrapper.lt(SysOrderItem::getUpdateTime, timeoutThreshold);
        // 一次性获取所有已支付状态的订单
        List<SysOrderItem> sysOrderItems = orderItemService.list(queryWrapper);
        long updateCount = sysOrderItems.size();
        if (CollUtil.isNotEmpty(sysOrderItems)) {
            // 按父订单ID分组处理
            Map<String, List<SysOrderItem>> orderGroupMap = sysOrderItems.stream()
                    .collect(Collectors.groupingBy(SysOrderItem::getParentOrderId));
            log.info("找到{}个父订单组，共{}条订单项需要处理", orderGroupMap.size(), updateCount);
            // 分别处理每个父订单组
            orderGroupMap.forEach((parentOrderId, orderItemsInGroup) -> {
                log.info("开始处理父订单组: {}，包含{}条订单项", parentOrderId, orderItemsInGroup.size());
                frozenOrderAndPushEvent(parentOrderId, orderItemsInGroup);
            });
            log.info("处理完成: 成功处理{}个父订单组，共{}条订单", orderGroupMap.size(), updateCount);
        } else {
            log.info("无符合条件订单");
        }
        stopWatch.stop();
        String resultMsg = updateCount > 0 ? "成功更新" + updateCount + "条订单状态为冻结" : "无符合条件订单";
        log.info("任务结束: {}", resultMsg);
        XxlJobHelper.log(resultMsg + "，耗时: {}ms", stopWatch.getTotalTimeMillis());
        return ReturnT.SUCCESS;
    }

    /**
     * 冻结订单并推送事件
     *
     * @param parentOrderId 父订单ID
     * @param orderItems    同一父订单下的订单明细项集合
     */
    private void frozenOrderAndPushEvent(String parentOrderId, List<SysOrderItem> orderItems) {
        boolean updateResult = orderProcessService.freezeOrderItem(orderItems);
        if (updateResult) {
            applicationEventPublisher.publishEvent(new OrderFrozenEvent(this, parentOrderId));
            log.info("为订单组 {} 发送了 OrderFrozenEvent 事件，包含{}条订单项", parentOrderId, orderItems.size());
        } else {
            log.warn("订单组 {} 状态更新失败，包含{}条订单项", parentOrderId, orderItems.size());
        }
    }
}
