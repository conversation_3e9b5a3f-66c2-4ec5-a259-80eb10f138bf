package com.knet.order.system.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/1 16:19
 * @description: KnetGroup到手价计算实现
 */
@Slf4j
@Component
public class ReversePriceCalculator {

    /**
     * KnetGroup手续费
     */
    public static final Float B2B_FEE = 1.1f;

    /**
     * 获取卖家到手价
     *
     * @param finalDollar 最终价格（美元，如19.09美元）
     * @return 原始价格（美元，保留2位小数），若无解返回null
     */
    public static BigDecimal getSellerOwingPrice(BigDecimal finalDollar) {
        return finalDollar;
    }

    /**
     * 获取knetGroup 到手价
     *
     * @param finalDollar 最终价格（美元整数）
     * @return 原始价格（美元整数），若无解返回null
     */
    public static BigDecimal getKgOwningPrice(BigDecimal finalDollar) {
        return finalDollar;
    }
}
