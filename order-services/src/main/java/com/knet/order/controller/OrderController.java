package com.knet.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.annotation.PermissionCheck;
import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.req.AdminOrderListQueryRequest;
import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.model.dto.req.OrderListQueryRequest;
import com.knet.order.model.dto.resp.*;
import com.knet.order.service.ISysOrderProcessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025/6/3 15:58
 * @description: 订单服务-订单控制器
 */
@Slf4j
@RestController
@RequestMapping("/order")
@Tag(name = "订单服务-订单控制器", description = "订单服务-订单控制器")
public class OrderController {
    @Resource
    private ISysOrderProcessService orderProcessService;

    /**
     * 从购物车创建订单
     *
     * @param request 创建订单请求
     * @return 创建订单响应
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @DistributedLock(key = "'createOrder:' + #request.hashCode()", expire = 2)
    @Loggable(value = "从购物车创建订单")
    @Operation(summary = "从购物车创建订单", description = "用户从购物车触发创建订单，订单创建完毕本地事务提交后，发送MQ消息到order.created")
    @PostMapping("/create")
    public HttpResult<CreateOrderResponse> createOrder(@Validated @RequestBody CreateOrderRequest request) {
        log.info("创建订单请求: {}", request);
        CreateOrderResponse response = orderProcessService.createOrderFromCart(request);
        return HttpResult.ok(response);
    }

    /**
     * n
     * 查询订单列表
     *
     * @param request 查询请求
     * @return 订单列表响应
     */
    @Cacheable(value = "orderList",
            key = "#request.userId + ':' " + "+ #request.pageNo + ':'" +
                    " + #request.pageSize + ':'" + " + (#request.orderId != null ? #request.orderId : 'all') + ':' " +
                    "+ (#request.searchType != null ? #request.searchType : 'all')",
            unless = "#result == null || #result.data == null || #result.data.records.isEmpty()")
    @Loggable(value = "查询订单列表")
    @Operation(summary = "查询订单列表", description = "分页查询订单列表，支持订单号搜索，展示父订单包含子订单的聚合信息")
    @PostMapping("/list")
    public HttpResult<IPage<OrderListResponse.ParentOrderResponse>> queryOrderList(@Validated @RequestBody OrderListQueryRequest request) {
        log.info("查询订单列表请求: {}", request);
        IPage<OrderListResponse.ParentOrderResponse> response = orderProcessService.queryOrderList(request);
        return HttpResult.ok(response);
    }

    /**
     * 获取订单详细信息
     *
     * @param parentOrderId 订单ID
     * @return 获取订单详情
     */
    @Loggable(value = "获取订单详细信息")
    @Operation(summary = "获取订单详细信息", description = "获取订单详细信息，包括订单聚合信息、商品数量、子订单详细信息、收货地址、支付信息")
    @GetMapping("/detail/{parentOrderId}")
    public HttpResult<OrderDetailResponse> getOrderDetail(
            @PathVariable("parentOrderId") @NotBlank(message = "订单ID不能为空") String parentOrderId) {
        log.info("获取订单详细信息请求: parentOrderId={}", parentOrderId);
        OrderDetailResponse response = orderProcessService.getOrderDetail(parentOrderId);
        return HttpResult.ok(response);
    }

    /**
     * 取消订单
     *
     * @param itemNo 订单No
     * @return 取消订单响应
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @DistributedLock(key = "'cancelOrder:' + #itemId", expire = 2)
    @Loggable(value = "取消订单")
    @Operation(summary = "取消订单", description = "用户取消订单，订单取消完毕本地事务提交后，发送MQ消息到order.cancel")
    @PostMapping("/cancel/{itemNo}")
    public HttpResult<CreateOrderResponse> cancelOrder(@PathVariable("itemNo") @NotBlank(message = "订单No不能为空") String itemNo) {
        log.info("取消订单请求:itemId= {}", itemNo);
        try {
            orderProcessService.cancelItemOrder(itemNo);
        } catch (Exception e) {
            log.error("取消订单 失败 :itemId= {}", itemNo);
            return HttpResult.error("取消失败");
        }
        return HttpResult.ok();
    }

    /**
     * 查询支付细节信息
     *
     * @param parentOrderId 父订单ID
     * @return 查询支付细节信息
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @GetMapping("/payment/{parentOrderId}")
    @Operation(summary = "查询支付细节信息", description = "查询支付细节信息")
    public HttpResult<PaymentDetailInfoResponse> queryPaymentInfo(
            @Parameter(description = "父订单ID", required = true, example = "ORDG-1934856345306337280")
            @PathVariable String parentOrderId) {
        log.info("查询支付细节信息: parentOrderId={}", parentOrderId);
        try {
            PaymentDetailInfoResponse response = orderProcessService.queryPaymentInfo(parentOrderId);
            return HttpResult.ok(response);
        } catch (Exception e) {
            log.error("查询支付细节信息: parentOrderId={}, error={}", parentOrderId, e.getMessage(), e);
            return HttpResult.error(e.getMessage());
        }
    }

    /**
     * 管理员查询所有用户订单列表
     *
     * @param request 管理员查询请求
     * @return 订单列表响应
     */
    @PermissionCheck(role = "admin")
    @PostMapping("/admin/list")
    @Operation(summary = "管理员查询所有订单", description = "管理员专用：查询所有用户订单，支持按用户筛选，返回结果包含用户账号信息")
    @Loggable(value = "管理员查询所有订单")
    public HttpResult<IPage<AdminOrderListResponse.AdminParentOrderResponse>> queryAllOrderList(@Validated @RequestBody AdminOrderListQueryRequest request) {
        log.info("管理员查询所有订单请求: {}", request);
        IPage<AdminOrderListResponse.AdminParentOrderResponse> result = orderProcessService.queryAllOrderList(request);
        return HttpResult.ok(result);
    }
}
