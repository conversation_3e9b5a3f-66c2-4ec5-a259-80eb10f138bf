package com.knet.notification.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.knet.common.dto.message.NotificationMessage;
import com.knet.common.enums.MessageChannel;
import com.knet.common.enums.MessageStatus;
import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.service.INotificationService;
import com.knet.notification.service.ISysMessageDeliveryService;
import com.knet.notification.service.ISysMessageService;
import com.knet.notification.strategy.INotificationSendStrategy;
import com.knet.notification.strategy.NotificationSendStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 通知服务实现 - 使用策略模式
 */
@Slf4j
@Service
public class NotificationServiceImpl implements INotificationService {

    @Resource
    private ISysMessageService sysMessageService;
    @Resource
    private ISysMessageDeliveryService sysMessageDeliveryService;
    @Resource
    private NotificationSendStrategyFactory strategyFactory;

    @Override
    public void sendNotification(NotificationMessage notificationMessage) {
        SysMessage saveMessage = sysMessageService.saveMessage(notificationMessage);
        if (BeanUtil.isNotEmpty(saveMessage)) {
            MessageChannel channel = notificationMessage.getDefaultChannel();
            try {
                INotificationSendStrategy strategy = strategyFactory.getStrategy(channel);
                boolean sendResult = strategy.sendNotification(saveMessage);
                updateDeliveryStatus(saveMessage, channel, sendResult);
            } catch (Exception e) {
                log.error("发送通知失败: {}", e.getMessage(), e);
                updateDeliveryStatus(saveMessage, channel, false);
            }
        }
    }

    /**
     * 更新消息发送状态
     *
     * @param sysMessage 系统消息
     * @param channel    消息通道
     * @param success    是否成功
     */
    private void updateDeliveryStatus(SysMessage sysMessage, MessageChannel channel, boolean success) {
        MessageStatus status = success ? MessageStatus.SUCCESS : MessageStatus.FAILED;
        sysMessageDeliveryService.updateDeliveryStatus(sysMessage.getId(), channel, status);
        String result = success ? "成功" : "失败";
        log.info("通知发送{}: 通道={}, 消息ID={}", result, channel, sysMessage.getId());
    }
}
