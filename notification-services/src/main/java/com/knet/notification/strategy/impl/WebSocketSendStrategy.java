package com.knet.notification.strategy.impl;

import com.knet.common.enums.MessageChannel;
import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.strategy.INotificationSendStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/8/15
 * @description: WebSocket发送策略实现
 */
@Slf4j
@Component
public class WebSocketSendStrategy implements INotificationSendStrategy {

    @Override
    public MessageChannel getSupportedChannel() {
        return MessageChannel.WEB_SOCKET;
    }

    @Override
    public boolean sendNotification(SysMessage sysMessage) {
        try {
            // TODO: 集成WebSocket推送服务
            log.info("WebSocket发送功能待实现，模拟发送成功: {}", sysMessage);
            String userId = sysMessage.getReceiverId();
            String content = sysMessage.getContent();
            log.info("WebSocket推送成功: 用户ID={}, 内容={}", userId, content);
            return true;
        } catch (Exception e) {
            log.error("WebSocket推送异常: {}", e.getMessage(), e);
            return false;
        }
    }
}
