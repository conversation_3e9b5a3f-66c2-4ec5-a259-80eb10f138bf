package com.knet.notification.strategy.impl;

import com.knet.common.enums.MessageChannel;
import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.strategy.INotificationSendStrategy;
import com.knet.notification.system.utils.GmailSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.knet.common.constants.NotificationConstants.KNET_B2B_ORDER_NOTIFICATION_SUB;

/**
 * <AUTHOR>
 * @date 2025/8/15
 * @description: 邮件发送策略实现
 */
@Slf4j
@Component
public class EmailSendStrategy implements INotificationSendStrategy {

    @Resource
    private GmailSender gmailSender;

    @Override
    public MessageChannel getSupportedChannel() {
        return MessageChannel.EMAIL;
    }

    @Override
    public boolean sendNotification(SysMessage sysMessage) {
        try {
            boolean sendResult = gmailSender.sendText(
                    sysMessage.getReceiverId(),
                    KNET_B2B_ORDER_NOTIFICATION_SUB,
                    sysMessage.getContent()
            );
            if (sendResult) {
                log.info("邮件发送成功: {}", sysMessage);
            } else {
                log.error("邮件发送失败: {}", sysMessage);
            }
            return sendResult;
        } catch (Exception e) {
            log.error("邮件发送异常: {}", e.getMessage(), e);
            return false;
        }
    }
}
