package com.knet.notification.strategy.impl;

import com.knet.common.enums.MessageChannel;
import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.strategy.INotificationSendStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/8/15
 * @description: 短信发送策略实现
 */
@Slf4j
@Component
public class SmsSendStrategy implements INotificationSendStrategy {

    @Override
    public MessageChannel getSupportedChannel() {
        return MessageChannel.SMS;
    }

    @Override
    public boolean sendNotification(SysMessage sysMessage) {
        try {
            // TODO: 集成短信服务提供商 (如阿里云SMS、腾讯云SMS等)
            log.info("短信发送功能待实现，模拟发送成功: {}", sysMessage);
            // 模拟发送逻辑
            String phoneNumber = sysMessage.getReceiverId();
            String content = sysMessage.getContent();
            // 这里应该调用实际的短信服务API
            // boolean sendResult = smsService.sendSms(phoneNumber, content);
            // 目前返回true模拟成功
            log.info("短信发送成功: 手机号={}, 内容={}", phoneNumber, content);
            return true;
        } catch (Exception e) {
            log.error("短信发送异常: {}", e.getMessage(), e);
            return false;
        }
    }
}
