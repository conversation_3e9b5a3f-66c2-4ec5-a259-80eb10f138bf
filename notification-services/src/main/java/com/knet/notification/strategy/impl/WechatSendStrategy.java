package com.knet.notification.strategy.impl;

import com.knet.common.enums.MessageChannel;
import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.strategy.INotificationSendStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/8/15
 * @description: 微信通知发送策略实现
 */
@Slf4j
@Component
public class WechatSendStrategy implements INotificationSendStrategy {

    @Override
    public MessageChannel getSupportedChannel() {
        return MessageChannel.WECHAT;
    }

    @Override
    public boolean sendNotification(SysMessage sysMessage) {
        try {
            // TODO: 集成微信公众号或企业微信API
            log.info("微信通知发送功能待实现，模拟发送成功: {}", sysMessage);
            String openId = sysMessage.getReceiverId();
            String content = sysMessage.getContent();
            // 这里应该调用微信API发送模板消息或企业微信消息
            // wechatService.sendTemplateMessage(openId, templateId, content);
            log.info("微信通知发送成功: OpenID={}, 内容={}", openId, content);
            return true;
        } catch (Exception e) {
            log.error("微信通知发送异常: {}", e.getMessage(), e);
            return false;
        }
    }
}
