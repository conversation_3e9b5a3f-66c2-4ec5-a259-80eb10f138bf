package com.knet.notification.strategy;

import com.knet.common.enums.MessageChannel;
import com.knet.notification.model.entity.SysMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 默认通知发送策略 - 用于处理暂未实现的通知类型
 */
@Slf4j
@Component
public class DefaultNotificationSendStrategy implements INotificationSendStrategy {

    @Override
    public MessageChannel getSupportedChannel() {
        // 这个策略不直接对应任何通道，仅作为兜底策略
        return null;
    }

    @Override
    public boolean sendNotification(SysMessage sysMessage) {
        log.warn("使用默认发送策略，该通知类型暂未实现具体发送逻辑: {}", sysMessage);
        // 返回false表示发送失败，但不会抛出异常
        return false;
    }
}
