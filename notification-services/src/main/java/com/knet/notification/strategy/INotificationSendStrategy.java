package com.knet.notification.strategy;

import com.knet.common.enums.MessageChannel;
import com.knet.notification.model.entity.SysMessage;

/**
 * <AUTHOR>
 * @date 2025/8/15
 * @description: 通知发送策略接口
 */
public interface INotificationSendStrategy {

    /**
     * 获取支持的消息通道
     *
     * @return 消息通道
     */
    MessageChannel getSupportedChannel();

    /**
     * 发送通知
     *
     * @param sysMessage 系统消息
     * @return 发送结果
     */
    boolean sendNotification(SysMessage sysMessage);
}
