package com.knet.notification.strategy;

import com.knet.common.enums.MessageChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 通知发送策略工厂 - 管理所有通知发送策略
 */
@Slf4j
@Component
public class NotificationSendStrategyFactory implements InitializingBean {

    @Resource
    private List<INotificationSendStrategy> notificationSendStrategies;
    @Resource
    private DefaultNotificationSendStrategy defaultStrategy;

    private final Map<MessageChannel, INotificationSendStrategy> strategyMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        for (INotificationSendStrategy strategy : notificationSendStrategies) {
            MessageChannel channel = strategy.getSupportedChannel();
            // 跳过默认策略，它不对应具体的消息通道
            if (channel != null) {
                strategyMap.put(channel, strategy);
                log.info("注册通知发送策略: {} -> {}", channel, strategy.getClass().getSimpleName());
            }
        }
        log.info("策略工厂初始化完成，共注册{}个发送策略", strategyMap.size());
    }

    /**
     * 根据消息通道获取对应的发送策略
     *
     * @param channel 消息通道
     * @return 发送策略
     */
    public INotificationSendStrategy getStrategy(MessageChannel channel) {
        INotificationSendStrategy strategy = strategyMap.get(channel);
        if (strategy == null) {
            log.warn("未找到{}通道的发送策略，使用默认策略", channel);
            return defaultStrategy;
        }
        return strategy;
    }

    /**
     * 检查是否支持指定的消息通道
     *
     * @param channel 消息通道
     * @return 是否支持
     */
    public boolean isSupported(MessageChannel channel) {
        return strategyMap.containsKey(channel);
    }

    /**
     * 获取所有已注册的消息通道
     *
     * @return 消息通道列表
     */
    public java.util.Set<MessageChannel> getSupportedChannels() {
        return strategyMap.keySet();
    }
}
