package com.knet.notification.controller;

import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.service.ISysMessageService;
import com.knet.notification.system.utils.GmailSender;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/20 14:55
 * @description: 消息控制器
 */
@Slf4j
@RestController
@RequestMapping("/message")
@Tag(name = "消息控制器", description = "消息控制器")
public class MessageController {

    @Resource
    private ISysMessageService sysMessageService;
    @Resource
    private GmailSender gmailSender;


    /**
     * 本地测试通过邮件发送
     *
     */
    @Deprecated
    @GetMapping("/send")
    public void sendMessage() throws Exception {
        SysMessage sysMessage = sysMessageService.getById(1);
        gmailSender.sendText(sysMessage.getReceiverId(), " 1 B2B Order Created Notification", sysMessage.getContent());
    }
}
