# 库存锁定策略模式重构文档

## 重构概述

参考payment模块中PaymentStrategy的策略模式结构，将goods模块中的IInventoryLockStrategy按照同样的结构进行了重构，提供了更好的扩展性和维护性。

## 重构内容

### 1. 目录结构调整

**重构前:**
```
goods-services/src/main/java/com/knet/goods/service/
├── IInventoryLockStrategy.java
└── impl/
    └── DefaultInventoryLockStrategy.java
```

**重构后:**
```
goods-services/src/main/java/com/knet/goods/strategy/
├── InventoryLockStrategy.java                    # 策略接口
├── InventoryLockStrategyFactory.java             # 策略工厂
└── impl/
    ├── AbstractInventoryLockStrategy.java        # 抽象基类
    ├── DefaultInventoryLockStrategy.java         # 默认策略实现
    └── PriorityInventoryLockStrategy.java        # 优先级策略实现
```

### 2. 核心组件

#### 2.1 策略接口 (InventoryLockStrategy)
```java
public interface InventoryLockStrategy {
    InventoryLockSuccessMessage lockInventoryWithDetails(CheckAndLockInvRequest request, String orderId, Long userId);
    String getStrategyName();
}
```

#### 2.2 策略工厂 (InventoryLockStrategyFactory)
- 自动发现和注册所有策略实现
- 提供策略获取和验证功能
- 支持默认策略获取

主要方法：
- `getStrategy(String strategyName)` - 获取指定策略
- `getDefaultStrategy()` - 获取默认策略
- `isSupported(String strategyName)` - 检查策略是否支持
- `getSupportedStrategies()` - 获取所有支持的策略

#### 2.3 抽象基类 (AbstractInventoryLockStrategy)
- 提供通用的库存锁定流程
- 处理用户信息获取和oneId重复检查
- 子类只需实现具体的锁定逻辑

#### 2.4 策略实现

**DefaultInventoryLockStrategy (默认策略)**
- 策略名称: "DEFAULT"
- 按创建时间降序锁定商品
- 分布式锁键: `inventory:lock:{sku}:{size}:{price}`

**PriorityInventoryLockStrategy (优先级策略)**
- 策略名称: "PRIORITY"
- 优先锁定价格较高的商品
- 分布式锁键: `inventory:priority:lock:{sku}:{size}:{price}`

### 3. 服务层调用方式

#### 3.1 InventoryServiceImpl 更新
```java
@Resource
private InventoryLockStrategyFactory inventoryLockStrategyFactory;

// 使用默认策略
InventoryLockStrategy strategy = inventoryLockStrategyFactory.getDefaultStrategy();
InventoryLockSuccessMessage lockSuccessMessage = strategy.lockInventoryWithDetails(request, parentOrderId, userId);
```

#### 3.2 新增 InventoryLockServiceImpl
提供了多种策略调用方式的示例：
- `lockInventoryWithDefaultStrategy()` - 使用默认策略
- `lockInventoryWithStrategy()` - 使用指定策略
- `lockInventoryWithPriorityStrategy()` - 使用优先级策略
- `getSupportedStrategies()` - 获取支持的策略列表

## 设计优势

### 1. 扩展性
- 新增策略只需实现InventoryLockStrategy接口
- 策略工厂自动发现和注册新策略
- 无需修改现有代码

### 2. 维护性
- 抽象基类减少代码重复
- 策略职责单一，易于维护
- 统一的错误处理和日志记录

### 3. 灵活性
- 支持运行时策略选择
- 可根据业务需求动态切换策略
- 支持策略验证和列举

### 4. 一致性
- 与payment模块策略模式保持一致
- 统一的命名规范和代码结构
- 相同的工厂模式实现

## 使用示例

### 基本使用
```java
@Resource
private InventoryLockStrategyFactory strategyFactory;

// 使用默认策略
InventoryLockStrategy defaultStrategy = strategyFactory.getDefaultStrategy();
InventoryLockSuccessMessage result = defaultStrategy.lockInventoryWithDetails(request, orderId, userId);

// 使用指定策略
InventoryLockStrategy priorityStrategy = strategyFactory.getStrategy("PRIORITY");
InventoryLockSuccessMessage result = priorityStrategy.lockInventoryWithDetails(request, orderId, userId);
```

### 策略验证
```java
if (strategyFactory.isSupported("CUSTOM")) {
    InventoryLockStrategy strategy = strategyFactory.getStrategy("CUSTOM");
    // 使用策略
}

// 获取所有支持的策略
List<String> strategies = strategyFactory.getSupportedStrategies();
```

## 兼容性

- 保持了原有的业务逻辑不变
- InventoryServiceImpl中的调用方式已更新
- 所有现有功能正常工作
- Maven编译测试通过

## 扩展指南

### 添加新策略
1. 继承AbstractInventoryLockStrategy
2. 实现lockSingleItem()方法
3. 提供唯一的策略名称
4. 添加@Component注解

示例：
```java
@Component
public class CustomInventoryLockStrategy extends AbstractInventoryLockStrategy {
    @Override
    protected InventoryLockSuccessMessage.LockedProductInfo lockSingleItem(SubOrderItemResp item, String account) {
        // 自定义锁定逻辑
    }
    
    @Override
    public String getStrategyName() {
        return "CUSTOM";
    }
}
```

策略工厂会自动发现并注册新策略，无需额外配置。
