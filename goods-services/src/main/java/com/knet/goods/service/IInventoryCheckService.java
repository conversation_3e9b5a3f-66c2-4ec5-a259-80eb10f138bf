package com.knet.goods.service;

import com.knet.goods.model.dto.req.InventoryCheckRequest;
import com.knet.goods.model.dto.resp.InventoryCheckResponse;

/**
 * <AUTHOR>
 * @date 2025/09/03
 * @description: 库存检查服务接口
 */
public interface IInventoryCheckService {

    /**
     * 批量检查库存
     *
     * @param request 库存检查请求
     * @return 库存检查结果
     */
    InventoryCheckResponse checkInventory(InventoryCheckRequest request);
}
