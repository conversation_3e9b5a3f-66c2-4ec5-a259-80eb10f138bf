package com.knet.goods.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.goods.model.dto.req.InnerKnetProductRequest;
import com.knet.goods.model.dto.req.ProductDetailsQueryRequest;
import com.knet.goods.model.dto.req.ProductQueryRequest;
import com.knet.goods.model.dto.req.QueryKnetProductRequest;
import com.knet.goods.model.dto.resp.*;
import com.knet.goods.model.entity.KnetProduct;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/2/10 13:23
 * @description: Product service 服务接口
 */
public interface IKnetProductService extends IService<KnetProduct> {

    /**
     * 查询商品列表 - 按sku分组
     *
     * @param request 查询请求
     * @return 商品列表
     */
    IPage<ProductBySkuDtoResp> queryProductGroupBySku(ProductQueryRequest request);

    /**
     * 查询商品详情
     *
     * @param request 查询请求
     * @return 商品详情
     * @see com.knet.goods.model.dto.req.ProductDetailsQueryRequest
     */
    List<ProductSkuSpecPriceDtoResp> queryProductDetails(ProductDetailsQueryRequest request);

    /**
     * 根据listingIds查询商品
     *
     * @param listingIds listingIds
     * @return 商品列表
     */
    List<String> queryByListingIds(List<String> listingIds);

    /**
     * 获取下架成功商品
     *
     * @param listingIds listingIds
     * @return 下架商品
     */
    List<OffSaleKnetProductResp.ProductDto> getOffSaleListingIds(List<String> listingIds);

    /**
     * 对外提供-查询商品列表
     *
     * @param request 查询请求
     * @return 商品列表
     */
    List<QueryKnetProductResp> queryKnetProductForApi(QueryKnetProductRequest request);

    /**
     * 检查商品是否上架
     *
     * @param oneId 商品oneId
     * @return 是否上架
     */
    Boolean checkProductOnSale(String oneId);

    /**
     * 根据oneId获取listingId
     *
     * @param oneId 商品oneId
     * @return listingId
     */
    String getListingIdByOneId(String oneId);

    /**
     * 获取本地所有上架状态商品的oneId
     *
     * @return 本地上架状态商品的oneId集合
     */
    Set<String> getLocalOnSaleOneIds();

    /**
     * 查询商品
     *
     * @param request 查询商品请求体
     * @return 返回查询结果
     */
    List<QueryKnetProductResp> queryKnetProduct(InnerKnetProductRequest request);

    /**
     * 获取商品上架状态和listingId信息
     *
     * @param oneId 商品oneId
     * @return 商品上架状态和listingId信息
     */
    ProductOnSaleInfoResp getProductOnSaleInfo(String oneId);
}
