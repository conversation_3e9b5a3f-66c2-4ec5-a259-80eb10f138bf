package com.knet.goods.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.common.enums.ProductMark;
import com.knet.goods.model.dto.req.*;
import com.knet.goods.model.dto.resp.*;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.model.entity.SysUpdateSysSkuEvents;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/19 16:18
 * @description: Product operation service 服务接口 - 处理所有非查询操作
 */
public interface IKnetProductOperationService extends IService<KnetProduct> {
    
    /**
     * 从knet 创建商品
     *
     * @param request request
     * @return 商品
     * @see com.knet.goods.model.dto.req.CreateKnetProductRequest.ProductDto
     */
    KnetProduct createByKnet(CreateKnetProductRequest.ProductDto request);

    /**
     * 允许部分失败 批量插入 商品
     *
     * @param list 商品列表
     */
    void insertIgnoreBatch(List<KnetProduct> list);

    /**
     * 更新商品为下架状态
     *
     * @param products listingID 列表
     * @return 下架操作listingIds
     */
    List<String> updateKnetProductForOffSale(List<OffSaleKnetProductRequest.ProductDto> products);

    /**
     * 更新商品价格
     *
     * @param productDto 商品价格参数
     * @return 操作结果
     */
    OffSaleKnetProductResp.ProductDto processKnetProductPrice(UpdatePriceKnetProductRequest.ProductDto productDto);

    /**
     * 处理 变更的商品信息
     *
     * @param updateEvents 变更事件列表
     */
    void updateKnetProductForSysSkuInfo(List<SysUpdateSysSkuEvents> updateEvents);

    /**
     * 批量更新 商品标识
     *
     * @param skus        商品sku列表
     * @param productMark 商品标识
     */
    void setProductModifyMark(List<String> skus, ProductMark productMark);

    /**
     * 重置商品标识为普通商品
     *
     * @param productMark 商品标识
     */
    void resetProductMarkToCommon(ProductMark productMark);

    /**
     * 将已存在且状态为ON_SALE的oneId对应的商品设置为OFF_SALE
     * 确保相同的oneId在数据库中只能存在一条记录且status为ON_SALE
     *
     * @param oneIds 商品oneId列表
     * @return 更新的记录数
     */
    int updateExistingProductsToOffSale(List<String> oneIds);

    /**
     * 锁定库存
     *
     * @param request 检查锁定库存请求体
     * @return 锁定结果
     */
    boolean lockInventory(CheckAndLockInvRequest request);

    /**
     * 基于现有商品创建新商品
     *
     * @param request 创建请求
     * @return 新商品的listingId
     */
    String createProductBasedOnExisting(CreateProductBasedOnExistingRequest request);

    /**
     * 根据oneId获取商品信息
     *
     * @param oneId 商品oneId
     * @return 商品信息
     */
    KnetProduct getProductByOneId(String oneId);

    /**
     * 将已上架商品锁定并标记为超卖替换
     *
     * @param oneId 商品oneId
     */
    void lockProductForOversellReplacement(String oneId);
}
