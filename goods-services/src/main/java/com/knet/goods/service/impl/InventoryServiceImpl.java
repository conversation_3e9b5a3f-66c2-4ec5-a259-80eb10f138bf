package com.knet.goods.service.impl;

import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.InventoryLockSuccessMessage;
import com.knet.common.exception.ServiceException;
import com.knet.goods.model.dto.req.CheckAndLockInvRequest;
import com.knet.goods.model.dto.third.resp.SubOrderItemResp;
import com.knet.goods.openfeign.ApiOrderServiceProvider;
import com.knet.goods.service.IInventoryLockStrategy;
import com.knet.goods.service.IInventoryService;
import com.knet.goods.service.IKnetProductOperationService;
import com.knet.goods.system.event.InventoryLockSuccessEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 15:04
 * @description: 库存接口实现
 */
@Service
@Slf4j
public class InventoryServiceImpl implements IInventoryService {
    @Resource
    private ApiOrderServiceProvider apiOrderServiceProvider;
    @Resource
    private IInventoryLockStrategy inventoryLockStrategy;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private IKnetProductOperationService iKnetProductOperationService;

    @Override
    public boolean checkAndLockInventory(String parentOrderId) {
        log.info("库存检查锁定 父订单 parentOrderId: {}", parentOrderId);
        HttpResult<List<SubOrderItemResp>> orderItemsByOrderId = apiOrderServiceProvider.getOrderItemsByOrderId(parentOrderId);
        List<SubOrderItemResp> items = orderItemsByOrderId.getData();
        boolean inventory = false;
        try {
            inventory = iKnetProductOperationService.lockInventory(new CheckAndLockInvRequest(items));
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return inventory;
    }

    @Override
    public boolean checkAndLockInventoryWithUserId(String parentOrderId, Long userId) {
        log.info("库存检查锁定 父订单 parentOrderId: {}, userId: {}", parentOrderId, userId);
        HttpResult<List<SubOrderItemResp>> orderItemsByOrderId = apiOrderServiceProvider.getOrderItemsByOrderId(parentOrderId);
        List<SubOrderItemResp> items = orderItemsByOrderId.getData();
        if (items == null || items.isEmpty()) {
            log.warn("订单商品列表为空: parentOrderId={}", parentOrderId);
            return false;
        }
        try {
            // 使用策略模式进行库存锁定
            InventoryLockSuccessMessage lockSuccessMessage = inventoryLockStrategy.lockInventoryWithDetails(
                    new CheckAndLockInvRequest(items), parentOrderId, userId);
            // 发送库存锁定成功事件
            sendInventoryLockSuccessEvent(lockSuccessMessage);
            log.info("库存锁定成功: parentOrderId={}, userId={}", parentOrderId, userId);
            return true;
        } catch (Exception e) {
            log.error("库存锁定失败: parentOrderId={}, error={}", parentOrderId, e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 发送库存锁定成功事件
     *
     * @param lockSuccessMessage 锁定成功消息
     */
    private void sendInventoryLockSuccessEvent(InventoryLockSuccessMessage lockSuccessMessage) {
        try {
            InventoryLockSuccessEvent event = new InventoryLockSuccessEvent(this, lockSuccessMessage);
            applicationEventPublisher.publishEvent(event);
            log.info("库存锁定成功事件发送成功: orderId={}", lockSuccessMessage.getOrderId());
        } catch (Exception e) {
            log.error("发送库存锁定成功事件异常: orderId={}, error={}", lockSuccessMessage.getOrderId(), e.getMessage());
        }
    }
}
