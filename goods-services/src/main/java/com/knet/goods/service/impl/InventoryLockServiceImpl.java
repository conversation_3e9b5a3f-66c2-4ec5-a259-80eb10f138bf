package com.knet.goods.service.impl;

import com.knet.common.dto.message.InventoryLockSuccessMessage;
import com.knet.common.exception.ServiceException;
import com.knet.goods.model.dto.req.CheckAndLockInvRequest;
import com.knet.goods.strategy.InventoryLockStrategy;
import com.knet.goods.strategy.InventoryLockStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/12/19 16:30
 * @description: 库存锁定服务实现
 * 演示如何使用策略工厂进行库存锁定
 */
@Slf4j
@Service
public class InventoryLockServiceImpl {

    @Resource
    private InventoryLockStrategyFactory inventoryLockStrategyFactory;

    /**
     * 使用默认策略锁定库存
     *
     * @param request 锁定请求
     * @param orderId 订单ID
     * @param userId  用户ID
     * @return 锁定结果
     */
    public InventoryLockSuccessMessage lockInventoryWithDefaultStrategy(CheckAndLockInvRequest request, String orderId, Long userId) {
        log.info("使用默认策略锁定库存: orderId={}, userId={}", orderId, userId);
        InventoryLockStrategy strategy = inventoryLockStrategyFactory.getDefaultStrategy();
        return strategy.lockInventoryWithDetails(request, orderId, userId);
    }

    /**
     * 使用指定策略锁定库存
     *
     * @param request      锁定请求
     * @param orderId      订单ID
     * @param userId       用户ID
     * @param strategyName 策略名称
     * @return 锁定结果
     */
    public InventoryLockSuccessMessage lockInventoryWithStrategy(CheckAndLockInvRequest request, String orderId, Long userId, String strategyName) {
        log.info("使用{}策略锁定库存: orderId={}, userId={}", strategyName, orderId, userId);
        if (!inventoryLockStrategyFactory.isSupported(strategyName)) {
            throw new ServiceException("不支持的库存锁定策略: " + strategyName);
        }
        InventoryLockStrategy strategy = inventoryLockStrategyFactory.getStrategy(strategyName);
        return strategy.lockInventoryWithDetails(request, orderId, userId);
    }

    /**
     * 使用优先级策略锁定库存
     *
     * @param request 锁定请求
     * @param orderId 订单ID
     * @param userId  用户ID
     * @return 锁定结果
     */
    public InventoryLockSuccessMessage lockInventoryWithPriorityStrategy(CheckAndLockInvRequest request, String orderId, Long userId) {
        log.info("使用优先级策略锁定库存: orderId={}, userId={}", orderId, userId);
        InventoryLockStrategy strategy = inventoryLockStrategyFactory.getStrategy("PRIORITY");
        return strategy.lockInventoryWithDetails(request, orderId, userId);
    }

    /**
     * 获取所有支持的策略
     *
     * @return 支持的策略列表
     */
    public java.util.List<String> getSupportedStrategies() {
        return inventoryLockStrategyFactory.getSupportedStrategies();
    }
}
