package com.knet.goods.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.goods.model.dto.req.ProductDetailsQueryRequest;
import com.knet.goods.model.dto.req.ProductPageQueryRequest;
import com.knet.goods.model.dto.req.ProductQueryRequest;
import com.knet.goods.model.dto.resp.ProductBySkuDtoResp;
import com.knet.goods.model.dto.resp.ProductPageQueryResp;
import com.knet.goods.model.dto.resp.ProductSkuSpecPriceDtoResp;
import com.knet.goods.model.dto.resp.SpecPriceDto;
import com.knet.goods.model.entity.KnetProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:10
 * @description: 商品 Mapper 接口
 */
@Mapper
public interface KnetProductMapper extends BaseMapper<KnetProduct> {

    /**
     * 查询商品列表-按sku分组
     *
     * @param request 查询条件
     * @return 商品列表
     */
    List<ProductBySkuDtoResp> queryProductGroupBySku(@Param("request") ProductQueryRequest request);

    /**
     * 查询商品列表-按sku分组-总数
     *
     * @param request 查询条件
     * @return 商品列表总数
     */
    Integer queryProductGroupBySkuCount(@Param("request") ProductQueryRequest request);

    /**
     * 查询商品详情
     *
     * @param request 查询条件
     * @return 商品详情
     */
    List<ProductSkuSpecPriceDtoResp> queryProductDetails(@Param("request") ProductDetailsQueryRequest request);

    /**
     * 允许部分失败 批量插入 商品
     *
     * @param list 商品列表
     */
    void insertIgnoreBatch(@Param("list") List<KnetProduct> list);

    // ==================== 分表临时表优化 ====================

    /**
     * 创建SKU临时表
     */
    void createTempSkuListTable();

    /**
     * 创建备注临时表
     */
    void createTempRemarkListTable();

    /**
     * 批量插入SKU到SKU临时表
     * @param skus SKU列表
     */
    void insertSkusToSkuTable(@Param("skus") List<String> skus);

    /**
     * 批量插入备注到备注临时表
     * @param remarks 备注列表
     */
    void insertRemarksToRemarkTable(@Param("remarks") List<String> remarks);

    /**
     * 使用分表策略查询商品列表
     * @param request 查询请求
     * @return 商品列表
     */
    List<ProductBySkuDtoResp> queryProductGroupBySkuWithSeparateTables(@Param("request") ProductQueryRequest request);

    /**
     * 使用分表策略查询商品总数
     * @param request 查询请求
     * @return 总数
     */
    Integer queryProductGroupBySkuCountWithSeparateTables(@Param("request") ProductQueryRequest request);

    /**
     * 删除SKU临时表
     */
    void dropTempSkuListTable();

    /**
     * 删除备注临时表
     */
    void dropTempRemarkListTable();

    /**
     * 查询商品详情价格信息（sku、尺码、价格、数量）)
     *
     * @param request 查询条件
     * @return 商品详情价格信息
     */
    List<SpecPriceDto> queryProductDetailsPriceInfo(@Param("request") ProductDetailsQueryRequest request);

    /**
     * 更新商品为下架状态
     *
     * @param oneIds oneIds
     * @param sku    sku
     */
    void updateKnetProductForOffSale(@Param("oneIds") List<String> oneIds, @Param("sku") String sku);

    /**
     * 分页查询商品列表
     *
     * @param request 查询条件
     * @return 商品列表
     */
    List<ProductPageQueryResp> queryProductsPage(@Param("request") ProductPageQueryRequest request);

    /**
     * 分页查询商品总数
     *
     * @param request 查询条件
     * @return 商品总数
     */
    Integer queryProductsPageCount(@Param("request") ProductPageQueryRequest request);
}
