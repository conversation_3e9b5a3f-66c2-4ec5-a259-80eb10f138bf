package com.knet.goods.model.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/8/28
 * @description: 价格聚合数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "价格聚合数据传输对象")
public class PriceAggregationData {

    @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sku;

    @Schema(description = "商品规格", requiredMode = Schema.RequiredMode.REQUIRED)
    private String spec;

    @Schema(description = "最低价格（单位：美分）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long minPrice;

    @Schema(description = "最高价格（单位：美分）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long maxPrice;
}
