package com.knet.goods.model.entity;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.context.ApiKeyContext;
import com.knet.common.dto.message.InventoryLockSuccessMessage;
import com.knet.common.enums.ProductMark;
import com.knet.common.enums.ProductStatus;
import com.knet.goods.model.dto.req.CreateKnetProductRequest;
import com.knet.goods.model.dto.resp.CreateKnetProductResp;
import com.knet.goods.model.dto.resp.ProductDtoResp;
import com.knet.goods.model.dto.resp.QueryKnetProductResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/2/19 16:05
 * @description: 商品表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "knet_product", description = "商品表")
@TableName("knet_product")
public class KnetProduct extends BaseEntity {

    @TableField("listing_Id")
    @Schema(description = "knet_product唯一标识符")
    private String listingId;

    @TableField("one_id")
    @Schema(description = "knetId")
    private String oneId;

    @TableField("sku")
    @Schema(description = "sku")
    private String sku;

    @TableField(value = "sku_indexed", insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    @Schema(description = "sku 检索索引")
    public String skuIndexed;

    @TableField("spec")
    @Schema(description = "尺码")
    private String spec;

    @TableField("brand")
    @Schema(description = "品牌")
    public String brand;

    @TableField("remarks")
    @Schema(description = "品名")
    public String remarks;

    @TableField("price")
    @Schema(description = "价格（单位：美分）", example = "1075")
    private Long price;

    @TableField("stock")
    @Schema(description = "库存数量")
    private Integer stock;

    @TableField("warehouse")
    @Schema(description = "存储仓库")
    private String warehouse;
    /**
     * @see ProductStatus
     */
    @TableField("status")
    @Schema(description = "商品状态 上架/下架")
    private ProductStatus status;

    @TableField("mark")
    @Schema(description = "商品标识 hot sale, new,common")
    private ProductMark mark;

    @TableField("creator")
    @Schema(description = "商品创建人")
    private String creator;

    @TableField("source")
    @Schema(description = "商品来源")
    private String source;

    public ProductDtoResp mapToProductDtoResp() {
        return ProductDtoResp.builder()
                .id(this.getId())
                .productId(this.getListingId())
                .createTime(this.getCreateTime())
                .updateTime(this.getUpdateTime())
                .oneId(this.getOneId())
                .sku(this.getSku())
                .spec(this.getSpec())
                .price(String.valueOf(NumberUtil.div(this.getPrice().longValue(), 100, 2)))
                .store(this.getWarehouse())
                .status(this.getStatus().getDescription())
                .build();
    }

    /**
     * mapRespProductDto to ProductDto
     *
     * @param productDto 商品dto
     * @return 商品dto
     */
    public static CreateKnetProductResp.ProductDto mapRespProductDto(KnetProduct productDto) {
        CreateKnetProductResp.ProductDto productDtoResp = new CreateKnetProductResp.ProductDto();
        productDtoResp.setOneId(productDto.getOneId());
        productDtoResp.setListingId(productDto.getListingId());
        return productDtoResp;
    }

    public QueryKnetProductResp mapToQueryKnetProductResp() {
        return QueryKnetProductResp.builder()
                .listingId(this.getListingId())
                .createTime(this.getCreateTime())
                .updateTime(this.getUpdateTime())
                .oneId(this.getOneId())
                .sku(this.getSku())
                .spec(this.getSpec())
                .price(String.valueOf(this.getPrice()))
                .status(this.getStatus().getDescription())
                .build();
    }

    public static InventoryLockSuccessMessage.ProductDetail createProductDetail(KnetProduct product) {
        return InventoryLockSuccessMessage.ProductDetail.builder()
                .productId(product.getId())
                .oneId(product.getOneId())
                .knetListingId(product.getListingId())
                .warehouse(product.getWarehouse())
                .source(product.getSource())
                .build();
    }


    public static KnetProduct initKnetProduct(CreateKnetProductRequest.ProductDto request) {
        return KnetProduct.builder()
                .oneId(request.getOneId())
                .sku(request.getSku())
                .spec(request.getSpec())
                .warehouse(request.getWarehouse())
                .status(ProductStatus.ON_SALE)
                .creator(ApiKeyContext.getApiKey())
                .source(request.getSource())
                .brand(request.getBrand())
                .remarks(request.getRemarks())
                .mark(ProductMark.COMMON)
                .build();
    }
}