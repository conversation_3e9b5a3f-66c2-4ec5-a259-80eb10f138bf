package com.knet.goods.model.dto.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/09/03
 * @description: 库存检查响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "库存检查响应")
@EqualsAndHashCode(callSuper = false)
public class InventoryCheckResponse extends BaseResponse {

    @Schema(description = "库存检查是否通过")
    private Boolean success;

    @Schema(description = "检查消息")
    private String message;

    @Schema(description = "库存不足的商品列表")
    private List<InsufficientInventoryItem> insufficientItems;

    /**
     * 库存不足商品项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "库存不足商品项")
    public static class InsufficientInventoryItem {

        @Schema(description = "商品SKU")
        private String sku;

        @Schema(description = "尺码")
        private String size;

        @Schema(description = "单价（美元）")
        private String price;

        @Schema(description = "需要数量")
        private Integer requiredQuantity;

        @Schema(description = "可用数量")
        private Integer availableQuantity;

        @Schema(description = "缺少数量")
        private Integer shortageQuantity;

        @Schema(description = "错误信息")
        private String errorMessage;
    }

    /**
     * 创建库存检查成功响应
     */
    public static InventoryCheckResponse success() {
        return InventoryCheckResponse.builder()
                .success(true)
                .message("库存检查通过")
                .build();
    }

    /**
     * 创建库存检查失败响应
     */
    public static InventoryCheckResponse failure(String message, List<InsufficientInventoryItem> insufficientItems) {
        return InventoryCheckResponse.builder()
                .success(false)
                .message(message)
                .insufficientItems(insufficientItems)
                .build();
    }
}
