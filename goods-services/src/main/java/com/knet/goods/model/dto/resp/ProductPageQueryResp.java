package com.knet.goods.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/8/21
 * @description: 商品分页查询响应DTO
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商品分页查询响应")
public class ProductPageQueryResp extends BaseResponse {

    @Schema(description = "商品ID")
    private Long id;

    @Schema(description = "knet_product唯一标识符")
    private String listingId;

    @Schema(description = "knet唯一标识符")
    private String oneId;

    @Schema(description = "sku")
    private String sku;

    @Schema(description = "尺码")
    private String spec;

    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "商品名称")
    private String remarks;

    @Schema(description = "价格（单位：美元）", example = "10.75")
    private String price;

    @Schema(description = "库存数量")
    private Integer stock;

    @Schema(description = "仓库")
    private String warehouse;

    @Schema(description = "商品状态")
    private String status;

    @Schema(description = "商品标识")
    private String mark;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "商品来源")
    private String source;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date updateTime;
}
