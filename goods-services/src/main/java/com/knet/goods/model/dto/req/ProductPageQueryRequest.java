package com.knet.goods.model.dto.req;

import com.knet.common.base.BasePageRequest;
import com.knet.common.enums.ProductStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/21 
 * @description: 商品分页查询请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "商品分页查询请求")
public class ProductPageQueryRequest extends BasePageRequest {

    @Schema(description = "商品状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private ProductStatus status;

    @Schema(description = "oneId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String oneId;

    @Schema(description = "listingId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String listingId;

    @Schema(description = "oneId列表", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> oneIds;

    @Schema(description = "listingId列表", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> listingIds;

    @Schema(description = "sku", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sku;

    @Schema(description = "品牌", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String brand;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remarks;

    @Schema(description = "尺码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String spec;

    @Schema(description = "仓库", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String warehouse;

    @Schema(description = "商品来源", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String source;

    @Schema(description = "创建人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String creator;
}
