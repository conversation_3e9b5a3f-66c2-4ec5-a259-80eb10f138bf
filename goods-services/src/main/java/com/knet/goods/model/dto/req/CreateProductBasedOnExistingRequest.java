package com.knet.goods.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * 基于现有商品创建新商品请求
 *
 * <AUTHOR>
 * @date 2025/09/02
 * @description: 超卖替换功能中商品服务用于基于现有商品创建新商品的请求参数，复制源商品所有属性仅修改oneId和listingId
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CreateProductBasedOnExistingRequest extends BaseRequest {

    @NotBlank(message = "源商品oneId不能为空")
    @Schema(description = "源商品oneId", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sourceOneId;

    @NotBlank(message = "目标商品oneId不能为空")
    @Schema(description = "目标商品oneId", requiredMode = Schema.RequiredMode.REQUIRED)
    private String targetOneId;
}
