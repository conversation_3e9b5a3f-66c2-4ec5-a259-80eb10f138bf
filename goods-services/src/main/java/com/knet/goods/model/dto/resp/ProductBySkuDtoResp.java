package com.knet.goods.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.knet.common.base.BaseResponse;
import com.knet.common.config.PriceIntegerSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/2/19 16:23
 * @description: 按照sku分组商品 返回dto
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductBySkuDtoResp extends BaseResponse {

    @Schema(description = "sku", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sku;

    @Schema(description = "品名")
    public String remarks;

    @Schema(description = "品牌")
    public String brand;

    @Schema(description = "商品图片")
    public String img;

    @Schema(description = "价格区间 最低价格（单位：美元）", example = "10")
    @JsonSerialize(using = PriceIntegerSerializer.class)
    private String minPrice;

    @Schema(description = "价格区间 最高价格（单位：美元）", example = "10")
    @JsonSerialize(using = PriceIntegerSerializer.class)
    private String maxPrice;

    @JsonIgnore
    @Schema(description = "原始最低价格（单位：美元）", example = "10")
    private String originalMinPrice;

    @JsonIgnore
    @Schema(description = "原始最高价格（单位：美元）", example = "10")
    private String originalMaxPrice;

    @Schema(description = "商品数量")
    private Integer total;

    @Schema(description = "商品标识", example = "hot sale, new")
    private String mark;
}
