package com.knet.goods.model.dto.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 商品上架状态查询响应
 *
 * <AUTHOR>
 * @date 2025/09/02
 * @description: 超卖替换功能中用于一次性获取商品上架状态和listingId信息的响应对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductOnSaleInfoResp extends BaseResponse {
    /**
     * 商品是否上架
     */
    @Schema(description = "商品是否上架")
    private Boolean onSale;
    /**
     * 商品listingId (仅当onSale=true时有值)
     */
    @Schema(description = "商品listingId（仅当onSale=true时有值）")
    private String listingId;
}
