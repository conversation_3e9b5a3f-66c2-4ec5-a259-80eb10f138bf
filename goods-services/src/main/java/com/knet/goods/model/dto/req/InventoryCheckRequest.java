package com.knet.goods.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/09/03
 * @description: 库存检查请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "库存检查请求")
@EqualsAndHashCode(callSuper = false)
public class InventoryCheckRequest extends BaseRequest {

    @Schema(description = "商品项列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品项列表不能为空")
    @Valid
    private List<InventoryCheckItem> items;

    /**
     * 库存检查商品项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "库存检查商品项")
    public static class InventoryCheckItem {

        @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "商品SKU不能为空")
        private String sku;

        @Schema(description = "尺码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "尺码不能为空")
        private String size;

        @Schema(description = "单价（策略价格，单位：美元）", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "单价不能为空")
        private String price;

        @Schema(description = "需要数量", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "需要数量不能为空")
        @Min(value = 1, message = "需要数量必须大于0")
        private Integer quantity;

        @Schema(description = "用户账号（用于排除用户自己的商品）")
        private String userAccount;
    }
}
