package com.knet.goods.system.schedule;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.knet.common.enums.ProductMark;
import com.knet.goods.model.dto.third.req.KnetGroupGetHotSkuRankDataReq;
import com.knet.goods.model.dto.third.resp.KnetProductMarketDataVo;
import com.knet.goods.service.IKnetProductOperationService;
import com.knet.goods.service.IThirdApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/30 14:16
 * @description: 更新产品标识
 */
@Slf4j
@Component
public class UpdateKnetProductMarkJob {
    @Resource
    private IThirdApiService thirdApiService;
    @Resource
    private IKnetProductOperationService iKnetProductOperationService;

    /**
     * 定时更新热销SKU标记
     * 每天凌晨1点执行，避开系统高峰期
     */
    @XxlJob("updateKnetProductMarkByHotSku")
    public ReturnT<String> updateKnetProductMarkByHotSku(String param) {
        List<String> skus = new ArrayList<>(20);
        try {
            KnetGroupGetHotSkuRankDataReq req = new KnetGroupGetHotSkuRankDataReq();
            List<KnetProductMarketDataVo> hotSkuRankData = thirdApiService.getHotSkuRankData();
            if (BeanUtil.isNotEmpty(hotSkuRankData)) {
                skus = hotSkuRankData.stream()
                        .map(KnetProductMarketDataVo::getSku)
                        .distinct()
                        .toList();
                log.info("从 kg 获取热销SKU数量: {}", skus.size());
            } else {
                log.warn("从 kg 未获取到热销SKU数据");
            }
        } catch (Exception e) {
            log.error("从kg,获取热销sku失败", e);
            return ReturnT.FAIL;
        }
        try {
            if (CollUtil.isNotEmpty(skus)) {
                iKnetProductOperationService.resetProductMarkToCommon(ProductMark.HOT_SALE);
                iKnetProductOperationService.setProductModifyMark(skus, ProductMark.HOT_SALE);
            }
            log.info("热销SKU标记更新完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("更新热销SKU标记失败: {}", e.getMessage(), e);
            return ReturnT.FAIL;
        }
    }
}
