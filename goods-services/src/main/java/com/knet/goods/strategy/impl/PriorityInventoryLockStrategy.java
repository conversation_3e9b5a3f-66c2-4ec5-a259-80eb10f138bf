package com.knet.goods.strategy.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.dto.message.InventoryLockSuccessMessage;
import com.knet.common.enums.ProductStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.goods.mapper.KnetProductMapper;
import com.knet.goods.model.dto.third.resp.SubOrderItemResp;
import com.knet.goods.model.entity.KnetProduct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/19 16:10
 * @description: 优先级库存锁定策略实现
 * 优先锁定价格较高的商品，适用于高价值商品优先销售的场景
 */
@Slf4j
@Component
public class PriorityInventoryLockStrategy extends AbstractInventoryLockStrategy {

    @Resource
    private KnetProductMapper knetProductMapper;


    /**
     * 优先级锁定单个商品项
     * 优先选择价格较高的商品进行锁定
     */
    @Override
    @DistributedLock(key = "'inventory:priority:lock:' + #item.sku + ':' + #item.size + ':' + #item.price", expire = 30)
    protected InventoryLockSuccessMessage.LockedProductInfo lockSingleItem(SubOrderItemResp item, String account) {
        Long strategyPriceCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
        Long originalPriceCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
        log.info("优先级策略库存锁定价格转换: SKU={}, 策略价格={}美分, 原始价格={}美分",
                item.getSku(), strategyPriceCents, originalPriceCents);

        // 查询可锁定的商品，按价格降序排列（优先锁定高价商品）
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(KnetProduct::getSku, item.getSku())
                .eq(KnetProduct::getPrice, originalPriceCents)
                .eq(KnetProduct::getSpec, item.getSize())
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .ne(StrUtil.isNotBlank(account), KnetProduct::getSource, account)
                .orderByDesc(KnetProduct::getPrice)  // 优先级策略：按价格降序
                .orderByDesc(KnetProduct::getCreateTime)  // 次要排序：按创建时间降序
                .last("LIMIT " + item.getCount());

        List<KnetProduct> productsToLock = knetProductMapper.selectList(queryWrapper);
        if (productsToLock.size() < item.getCount()) {
            log.error("优先级策略商品: {} 尺码: {} 原始价格: {} 库存不足，需要{}个，实际只有{}个",
                    item.getSku(), item.getSize(), originalPriceCents, item.getCount(), productsToLock.size());
            throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 库存不足");
        }

        List<Long> idsToLock = productsToLock.stream().map(KnetProduct::getId).toList();
        int updatedCount = 0;
        for (Long productId : idsToLock) {
            KnetProduct currentProduct = knetProductMapper.selectById(productId);
            if (currentProduct == null || !ProductStatus.ON_SALE.equals(currentProduct.getStatus())) {
                log.warn("优先级策略商品状态已变更，跳过锁定: productId={}, currentStatus={}",
                        productId, currentProduct != null ? currentProduct.getStatus() : "NOT_FOUND");
                continue;
            }

            LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(KnetProduct::getId, productId)
                    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                    .eq(KnetProduct::getVersion, currentProduct.getVersion())
                    .set(KnetProduct::getStatus, ProductStatus.LOCKED)
                    .set(KnetProduct::getVersion, currentProduct.getVersion() + 1);
            int singleUpdated = knetProductMapper.update(null, updateWrapper);
            if (singleUpdated > 0) {
                updatedCount++;
                log.debug("优先级策略商品锁定成功: productId={}, price={}, version={}->{}",
                        productId, currentProduct.getPrice(), currentProduct.getVersion(), currentProduct.getVersion() + 1);
            } else {
                log.warn("优先级策略商品锁定失败，可能被其他线程修改: productId={}, version={}",
                        productId, currentProduct.getVersion());
            }
        }

        if (updatedCount != idsToLock.size()) {
            log.error("优先级策略商品锁定失败，期望锁定{}个，实际锁定{}个。可能原因：商品状态已变更或被其他实例锁定",
                    idsToLock.size(), updatedCount);
            throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 锁定失败，商品状态已变更");
        }

        List<KnetProduct> lockedProducts = knetProductMapper.selectByIds(idsToLock);
        List<String> oneIds = lockedProducts.stream().map(KnetProduct::getOneId).toList();
        List<String> listingIds = lockedProducts.stream().map(KnetProduct::getListingId).toList();
        log.info("优先级策略商品锁定成功 - SKU: {}, 尺码: {}, 原始价格: {}, 锁定数量: {}, 商品ID列表: {},商品oneId: {},商品listingId: {}",
                item.getSku(), item.getSize(), originalPriceCents, updatedCount, idsToLock, oneIds, listingIds);

        List<InventoryLockSuccessMessage.ProductDetail> productDetails = lockedProducts
                .stream()
                .map(KnetProduct::createProductDetail)
                .toList();
        return InventoryLockSuccessMessage.LockedProductInfo.builder()
                .sku(item.getSku())
                .size(item.getSize())
                .price(item.getPrice())
                .productDetails(productDetails)
                .build();
    }

    @Override
    public String getStrategyName() {
        return "PRIORITY";
    }
}
