package com.knet.goods.strategy;

import com.knet.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/12/19 16:00
 * @description: 库存锁定策略工厂
 */
@Slf4j
@Component
public class InventoryLockStrategyFactory {

    @Resource
    private List<InventoryLockStrategy> inventoryLockStrategies;

    private final Map<String, InventoryLockStrategy> strategyMap = new ConcurrentHashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        log.info("初始化库存锁定策略工厂，共发现 {} 个库存锁定策略", inventoryLockStrategies.size());
        for (InventoryLockStrategy strategy : inventoryLockStrategies) {
            String strategyName = strategy.getStrategyName();
            strategyMap.put(strategyName, strategy);
            log.info("注册库存锁定策略: {} -> {}", strategyName, strategy.getClass().getSimpleName());
        }
        log.info("库存锁定策略工厂初始化完成，支持的策略: {}", strategyMap.keySet());
    }

    /**
     * 获取库存锁定策略
     *
     * @param strategyName 策略名称
     * @return 库存锁定策略
     */
    public InventoryLockStrategy getStrategy(String strategyName) {
        InventoryLockStrategy strategy = strategyMap.get(strategyName);
        if (strategy == null) {
            throw new ServiceException("不支持的库存锁定策略: " + strategyName);
        }
        return strategy;
    }

    /**
     * 获取默认策略
     *
     * @return 默认库存锁定策略
     */
    public InventoryLockStrategy getDefaultStrategy() {
        return getStrategy("DEFAULT");
    }

    /**
     * 检查是否支持指定的策略
     *
     * @param strategyName 策略名称
     * @return 是否支持
     */
    public boolean isSupported(String strategyName) {
        return strategyMap.containsKey(strategyName);
    }

    /**
     * 获取所有支持的策略名称
     *
     * @return 支持的策略名称列表
     */
    public List<String> getSupportedStrategies() {
        return List.copyOf(strategyMap.keySet());
    }
}
