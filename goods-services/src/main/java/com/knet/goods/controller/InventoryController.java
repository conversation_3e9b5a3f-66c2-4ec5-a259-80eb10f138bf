package com.knet.goods.controller;

import com.knet.common.base.HttpResult;
import com.knet.goods.model.dto.req.InventoryCheckRequest;
import com.knet.goods.model.dto.resp.InventoryCheckResponse;
import com.knet.goods.service.IInventoryCheckService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/09/03
 * @description: 库存管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/inventory")
@Tag(name = "库存管理接口", description = "提供库存检查等功能")
public class InventoryController {

    @Resource
    private IInventoryCheckService inventoryCheckService;

    /**
     * 批量检查库存
     *
     * @param request 库存检查请求
     * @return 库存检查结果
     */
    @PostMapping("/check")
    @Operation(summary = "批量检查库存", description = "检查多个商品的库存是否充足")
    public HttpResult<InventoryCheckResponse> checkInventory(@Validated @RequestBody InventoryCheckRequest request) {
        log.info("接收到库存检查请求，商品种类数: {}", request.getItems().size());
        InventoryCheckResponse response = inventoryCheckService.checkInventory(request);
        return HttpResult.ok(response);
    }
}
