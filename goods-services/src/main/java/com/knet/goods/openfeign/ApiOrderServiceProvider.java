package com.knet.goods.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.goods.model.dto.third.resp.KnetSubOrderGroupDto;
import com.knet.goods.model.dto.third.resp.KnetSysOrderItem;
import com.knet.goods.model.dto.third.resp.SubOrderItemResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:49
 * @description: 订单服务 API客户端
 */
@FeignClient(name = "order-services", path = "orderService/api")
public interface ApiOrderServiceProvider {

    /**
     * 根据订单ID获取 订单商品明细信息
     *
     * @param prentOrderId 订单ID
     * @return 订单商品明细信息
     */
    @GetMapping("/order/{prentOrderId}")
    @Operation(summary = "根据订单ID获取订单商品明细信息")
    HttpResult<List<SubOrderItemResp>> getOrderItemsByOrderId(
            @Parameter(description = "订单ID", required = true, example = "ORC-123456789012345678")
            @PathVariable("prentOrderId") String prentOrderId);

    /**
     * 根据父订单号获取订单item详情列表
     *
     * @param prentOrderId 父订单号
     * @return 订单项详情
     */
    @GetMapping("/order/items/{prentOrderId}")
    @Operation(summary = "根据父订单号获取订单item详情列表", description = "供其他服务调用，根据父订单号获取订单item详情列表")
    HttpResult<List<KnetSysOrderItem>> getOrderItemList(
            @Parameter(description = "父订单号", required = true, example = "1")
            @PathVariable("prentOrderId") String prentOrderId);

    /**
     * 根据itemId获取订单item详情
     *
     * @param itemId 订单项ID
     * @return 订单项详情
     */
    @GetMapping("/order/item/{itemId}")
    @Operation(summary = "根据itemId获取订单item详情", description = "供其他服务调用，根据itemId获取订单item详情")
    HttpResult<KnetSysOrderItem> getOrderItemDetail(
            @Parameter(description = "订单ID", required = true, example = "1")
            @PathVariable("itemId") String itemId);

    /**
     * 根据订单ID获取 订单信息
     *
     * @param prentOrderId 订单ID
     * @return 订单信息
     */
    @GetMapping("/order/group/{prentOrderId}")
    @Operation(summary = "根据订单ID获取订单信息", description = "供其他服务调用，获取订单信息")
    HttpResult<KnetSubOrderGroupDto> getOrderGroupByOrderId(
            @Parameter(description = "订单ID", required = true, example = "ORC-123456789012345678")
            @PathVariable("prentOrderId") String prentOrderId);

    /**
     * 检查oneId冲突
     *
     * @param oneIds         要检查的oneId列表
     * @param excludeOrderId 排除的订单ID
     * @return 冲突的oneId列表
     */
    @PostMapping("/order/check-oneid-conflicts")
    @Operation(summary = "检查oneId冲突", description = "检查指定的oneId是否已被其他订单使用")
    HttpResult<List<String>> checkOneIdConflicts(
            @RequestParam("oneIds") List<String> oneIds,
            @RequestParam("excludeOrderId") String excludeOrderId);
}
