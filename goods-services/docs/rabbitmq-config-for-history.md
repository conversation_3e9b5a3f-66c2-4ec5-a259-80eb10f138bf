# RabbitMQ配置 - 商品历史记录

## 1. 交换机和队列配置

### 1.1 RabbitMQ配置类

```java
package com.knet.goods.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ProductHistoryRabbitConfig {
    
    // 交换机名称
    public static final String PRODUCT_HISTORY_EXCHANGE = "product.history.exchange";
    
    // 队列名称
    public static final String PRODUCT_HISTORY_QUEUE = "product.history.save.queue";
    public static final String PRODUCT_HISTORY_DLQ = "product.history.save.dlq";
    
    // 路由键
    public static final String PRODUCT_HISTORY_ROUTING_KEY = "product.history.save";
    public static final String PRODUCT_HISTORY_DLQ_ROUTING_KEY = "product.history.save.dlq";
    
    /**
     * 创建主题交换机
     */
    @Bean
    public TopicExchange productHistoryExchange() {
        return ExchangeBuilder
                .topicExchange(PRODUCT_HISTORY_EXCHANGE)
                .durable(true)
                .build();
    }
    
    /**
     * 创建历史记录保存队列
     */
    @Bean
    public Queue productHistoryQueue() {
        return QueueBuilder
                .durable(PRODUCT_HISTORY_QUEUE)
                .withArgument("x-dead-letter-exchange", PRODUCT_HISTORY_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", PRODUCT_HISTORY_DLQ_ROUTING_KEY)
                .withArgument("x-message-ttl", 300000) // 5分钟TTL
                .build();
    }
    
    /**
     * 创建死信队列
     */
    @Bean
    public Queue productHistoryDeadLetterQueue() {
        return QueueBuilder
                .durable(PRODUCT_HISTORY_DLQ)
                .build();
    }
    
    /**
     * 绑定主队列到交换机
     */
    @Bean
    public Binding productHistoryBinding() {
        return BindingBuilder
                .bind(productHistoryQueue())
                .to(productHistoryExchange())
                .with(PRODUCT_HISTORY_ROUTING_KEY);
    }
    
    /**
     * 绑定死信队列到交换机
     */
    @Bean
    public Binding productHistoryDlqBinding() {
        return BindingBuilder
                .bind(productHistoryDeadLetterQueue())
                .to(productHistoryExchange())
                .with(PRODUCT_HISTORY_DLQ_ROUTING_KEY);
    }
}
```

### 1.2 消息重试配置

```yaml
# application.yml
spring:
  rabbitmq:
    host: ${RABBITMQ_HOST:***********}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:admin}
    password: ${RABBITMQ_PASSWORD:admin}
    virtual-host: ${RABBITMQ_VHOST:/}
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true
    template:
      mandatory: true
      retry:
        enabled: true
        initial-interval: 1000
        max-attempts: 3
        max-interval: 10000
        multiplier: 1.0
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          max-interval: 10000
          multiplier: 2.0
        default-requeue-rejected: false
```

## 2. 消息发送器实现

```java
package com.knet.goods.service.message;

import com.knet.goods.config.ProductHistoryRabbitConfig;
import com.knet.goods.model.dto.message.ProductHistoryMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Slf4j
@Component
public class ProductHistoryMessageSender {
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    public void sendHistoryMessage(ProductHistoryMessage message) {
        try {
            // 生成消息ID用于确认
            String messageId = UUID.randomUUID().toString();
            CorrelationData correlationData = new CorrelationData(messageId);
            
            // 发送消息
            rabbitTemplate.convertAndSend(
                ProductHistoryRabbitConfig.PRODUCT_HISTORY_EXCHANGE,
                ProductHistoryRabbitConfig.PRODUCT_HISTORY_ROUTING_KEY,
                message,
                correlationData
            );
            
            log.info("发送商品历史记录消息成功: messageId={}, listingId={}, operationType={}", 
                    messageId, message.getListingId(), message.getOperationType());
                    
        } catch (Exception e) {
            log.error("发送商品历史记录消息失败: listingId={}, operationType={}", 
                    message.getListingId(), message.getOperationType(), e);
            
            // 可以在这里实现本地消息表保存，用于后续重试
            saveToLocalMessageTable(message, e.getMessage());
        }
    }
    
    private void saveToLocalMessageTable(ProductHistoryMessage message, String errorMessage) {
        // 实现本地消息表保存逻辑
        // 这里可以保存到sys_message_retry表中，由定时任务重试
        log.warn("消息发送失败，已保存到本地重试表: listingId={}", message.getListingId());
    }
}
```

## 3. 消息消费者实现

```java
package com.knet.goods.listener;

import com.knet.goods.config.ProductHistoryRabbitConfig;
import com.knet.goods.model.dto.message.ProductHistoryMessage;
import com.knet.goods.service.IKnetProductHistoryService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
public class ProductHistoryMessageConsumer {
    
    @Autowired
    private IKnetProductHistoryService historyService;
    
    @RabbitListener(queues = ProductHistoryRabbitConfig.PRODUCT_HISTORY_QUEUE)
    public void handleHistoryMessage(ProductHistoryMessage historyMessage, 
                                   Message message, 
                                   Channel channel) throws IOException {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            log.info("接收到商品历史记录消息: listingId={}, operationType={}, deliveryTag={}", 
                    historyMessage.getListingId(), historyMessage.getOperationType(), deliveryTag);
            
            // 处理历史记录保存
            historyService.saveHistory(historyMessage);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("处理商品历史记录消息成功: listingId={}, deliveryTag={}", 
                    historyMessage.getListingId(), deliveryTag);
                    
        } catch (Exception e) {
            log.error("处理商品历史记录消息失败: listingId={}, deliveryTag={}", 
                    historyMessage.getListingId(), deliveryTag, e);
            
            // 获取重试次数
            Integer retryCount = (Integer) message.getMessageProperties().getHeaders().get("x-retry-count");
            if (retryCount == null) {
                retryCount = 0;
            }
            
            if (retryCount < 3) {
                // 重新入队，等待重试
                channel.basicNack(deliveryTag, false, true);
                log.warn("消息处理失败，重新入队重试: listingId={}, retryCount={}", 
                        historyMessage.getListingId(), retryCount + 1);
            } else {
                // 超过重试次数，拒绝消息（会进入死信队列）
                channel.basicNack(deliveryTag, false, false);
                log.error("消息处理失败，超过最大重试次数，进入死信队列: listingId={}", 
                        historyMessage.getListingId());
            }
        }
    }
    
    /**
     * 处理死信队列消息
     */
    @RabbitListener(queues = ProductHistoryRabbitConfig.PRODUCT_HISTORY_DLQ)
    public void handleDeadLetterMessage(ProductHistoryMessage historyMessage, 
                                      Message message, 
                                      Channel channel) throws IOException {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            log.error("接收到死信队列消息: listingId={}, operationType={}", 
                    historyMessage.getListingId(), historyMessage.getOperationType());
            
            // 记录到错误日志或发送告警
            recordFailedMessage(historyMessage, "消息处理失败，已进入死信队列");
            
            // 确认死信消息
            channel.basicAck(deliveryTag, false);
            
        } catch (Exception e) {
            log.error("处理死信队列消息异常: listingId={}", historyMessage.getListingId(), e);
            channel.basicNack(deliveryTag, false, false);
        }
    }
    
    private void recordFailedMessage(ProductHistoryMessage message, String reason) {
        // 记录失败消息，可以保存到专门的错误表中
        // 或者发送告警通知
        log.error("历史记录消息处理失败: listingId={}, reason={}", message.getListingId(), reason);
    }
}
```

## 4. 消息幂等性处理

```java
package com.knet.goods.service.impl;

import com.knet.goods.model.dto.message.ProductHistoryMessage;
import com.knet.goods.model.entity.KnetProductHistory;
import com.knet.goods.service.IKnetProductHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ProductHistoryServiceImpl implements IKnetProductHistoryService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String HISTORY_MESSAGE_KEY_PREFIX = "history:msg:";
    private static final long MESSAGE_EXPIRE_TIME = 24; // 24小时过期
    
    @Override
    public void saveHistory(ProductHistoryMessage message) {
        // 生成消息唯一键
        String messageKey = generateMessageKey(message);
        
        // 检查消息是否已处理（幂等性）
        if (isMessageProcessed(messageKey)) {
            log.warn("消息已处理，跳过: listingId={}, operationType={}", 
                    message.getListingId(), message.getOperationType());
            return;
        }
        
        try {
            // 标记消息正在处理
            markMessageProcessing(messageKey);
            
            // 转换并保存历史记录
            KnetProductHistory history = convertToHistory(message);
            save(history);
            
            // 标记消息处理完成
            markMessageProcessed(messageKey);
            
            log.info("历史记录保存成功: listingId={}, operationType={}", 
                    message.getListingId(), message.getOperationType());
                    
        } catch (Exception e) {
            // 清除处理标记
            clearMessageProcessing(messageKey);
            throw e;
        }
    }
    
    private String generateMessageKey(ProductHistoryMessage message) {
        return HISTORY_MESSAGE_KEY_PREFIX + 
               message.getListingId() + ":" + 
               message.getOperationType() + ":" + 
               message.getOperationTime().getTime();
    }
    
    private boolean isMessageProcessed(String messageKey) {
        return redisTemplate.hasKey(messageKey);
    }
    
    private void markMessageProcessing(String messageKey) {
        redisTemplate.opsForValue().set(messageKey, "PROCESSING", MESSAGE_EXPIRE_TIME, TimeUnit.HOURS);
    }
    
    private void markMessageProcessed(String messageKey) {
        redisTemplate.opsForValue().set(messageKey, "PROCESSED", MESSAGE_EXPIRE_TIME, TimeUnit.HOURS);
    }
    
    private void clearMessageProcessing(String messageKey) {
        redisTemplate.delete(messageKey);
    }
    
    private KnetProductHistory convertToHistory(ProductHistoryMessage message) {
        return KnetProductHistory.builder()
                .originalId(message.getOriginalId())
                .listingId(message.getListingId())
                .operationType(message.getOperationType())
                .operationTime(message.getOperationTime())
                .operator(message.getOperator())
                .operationSource(message.getOperationSource())
                .beforeData(message.getBeforeData())
                .afterData(message.getAfterData())
                .changedFields(String.join(",", message.getChangedFields()))
                .remark(message.getRemark())
                .build();
    }
}
```

## 5. 监控和告警

### 5.1 消息队列监控

```java
package com.knet.goods.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ProductHistoryQueueMonitor {
    
    @Autowired
    private RabbitAdmin rabbitAdmin;
    
    /**
     * 监控队列积压情况
     */
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void monitorQueueBacklog() {
        try {
            // 检查主队列消息数量
            Properties queueInfo = rabbitAdmin.getQueueProperties("product.history.save.queue");
            if (queueInfo != null) {
                Integer messageCount = (Integer) queueInfo.get("QUEUE_MESSAGE_COUNT");
                if (messageCount != null && messageCount > 1000) {
                    log.warn("商品历史记录队列积压严重: messageCount={}", messageCount);
                    // 发送告警
                    sendAlert("队列积压告警", "商品历史记录队列消息数量: " + messageCount);
                }
            }
            
            // 检查死信队列消息数量
            Properties dlqInfo = rabbitAdmin.getQueueProperties("product.history.save.dlq");
            if (dlqInfo != null) {
                Integer dlqMessageCount = (Integer) dlqInfo.get("QUEUE_MESSAGE_COUNT");
                if (dlqMessageCount != null && dlqMessageCount > 0) {
                    log.error("死信队列有消息: messageCount={}", dlqMessageCount);
                    sendAlert("死信队列告警", "死信队列消息数量: " + dlqMessageCount);
                }
            }
            
        } catch (Exception e) {
            log.error("监控队列状态失败", e);
        }
    }
    
    private void sendAlert(String title, String content) {
        // 实现告警发送逻辑，可以是邮件、短信或钉钉等
        log.error("告警: {} - {}", title, content);
    }
}
```

这个配置提供了完整的RabbitMQ异步消息机制，包括：

1. **完整的队列配置**: 主队列、死信队列和相关绑定
2. **消息发送器**: 支持确认机制和本地重试表
3. **消息消费者**: 支持手动确认和重试机制
4. **幂等性处理**: 基于Redis的消息去重
5. **监控告警**: 队列积压和死信监控

这确保了历史记录保存的可靠性和高性能。
