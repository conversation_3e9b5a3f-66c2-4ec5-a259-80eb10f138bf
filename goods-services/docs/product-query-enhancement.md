# 商品查询功能增强

## 概述

为 `queryProductGroupBySku` 方法添加了两个新的筛选功能：
1. 根据商品数量区间筛选（total字段）
2. 根据尺码筛选（spec字段）

## 新增参数

### ProductQueryRequest 新增字段

```java
@Schema(description = "尺码筛选", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
private String spec;

@Schema(description = "商品数量最小值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
private Integer minTotal;

@Schema(description = "商品数量最大值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
private Integer maxTotal;
```

## 使用示例

### 1. 按尺码筛选

```json
{
  "pageNo": 1,
  "pageSize": 10,
  "spec": "US 10"
}
```

### 2. 按商品数量区间筛选

```json
{
  "pageNo": 1,
  "pageSize": 10,
  "minTotal": 5,
  "maxTotal": 20
}
```

### 3. 组合筛选

```json
{
  "pageNo": 1,
  "pageSize": 10,
  "spec": "US 9",
  "minTotal": 3,
  "maxTotal": 15,
  "brand": "Nike"
}
```

### 4. 只设置最小值

```json
{
  "pageNo": 1,
  "pageSize": 10,
  "minTotal": 10
}
```

### 5. 只设置最大值

```json
{
  "pageNo": 1,
  "pageSize": 10,
  "maxTotal": 5
}
```

## 实现细节

### SQL 查询优化

1. **spec筛选**：在WHERE子句中添加了对`kp.spec`字段的过滤
2. **total数量筛选**：使用HAVING子句在GROUP BY之后进行筛选，因为total是聚合函数COUNT(*)的结果

### 支持的查询方式

- 传统查询方式：直接使用IN条件查询
- 临时表优化查询：当SKU或商品名称数量较大时，使用临时表提升查询性能

### 修改的文件

1. `ProductQueryRequest.java` - 添加新的筛选参数
2. `KnetProductMapper.xml` - 修改SQL查询逻辑
   - `queryProductGroupBySku` - 主查询方法
   - `queryProductGroupBySkuCount` - 查询总数方法
   - `queryProductGroupBySkuWithSeparateTables` - 分表优化查询
   - `queryProductGroupBySkuCountWithSeparateTables` - 分表优化查询总数

## 测试

创建了完整的测试用例 `ProductControllerTest.java`，包括：
- 尺码筛选测试
- 数量区间筛选测试
- 组合筛选测试
- 边界值测试

## 注意事项

1. `spec` 参数支持精确匹配，如需模糊匹配可后续扩展
2. `minTotal` 和 `maxTotal` 可以单独使用或组合使用
3. 所有新增参数都是可选的，不影响现有功能
4. 临时表优化策略同样支持新的筛选条件
