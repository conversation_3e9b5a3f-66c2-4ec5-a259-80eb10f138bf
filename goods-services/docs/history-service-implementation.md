# 历史记录服务实现 - 完整的服务层和接口

## 1. 服务接口定义

### 1.1 IKnetProductHistoryService接口

```java
package com.knet.goods.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.goods.model.dto.message.ProductHistoryMessage;
import com.knet.goods.model.dto.req.ProductHistoryQueryRequest;
import com.knet.goods.model.dto.resp.ProductHistoryQueryResp;
import com.knet.goods.model.entity.KnetProductHistory;
import com.knet.goods.model.enums.HistoryOperationType;

import java.util.Date;
import java.util.List;

/**
 * 商品历史记录服务接口
 */
public interface IKnetProductHistoryService extends IService<KnetProductHistory> {
    
    /**
     * 保存历史记录（从消息队列调用）
     */
    void saveHistory(ProductHistoryMessage message);
    
    /**
     * 分页查询历史记录
     */
    IPage<ProductHistoryQueryResp> queryHistoryPage(ProductHistoryQueryRequest request);
    
    /**
     * 查询特定商品的历史记录
     */
    List<ProductHistoryQueryResp> queryByListingId(String listingId);
    
    /**
     * 查询特定操作类型的历史记录
     */
    List<ProductHistoryQueryResp> queryByOperationType(HistoryOperationType operationType, 
                                                      Date startTime, Date endTime);
    
    /**
     * 查询特定操作人的历史记录
     */
    List<ProductHistoryQueryResp> queryByOperator(String operator, Date startTime, Date endTime);
    
    /**
     * 获取历史记录统计信息
     */
    Map<String, Object> getHistoryStatistics(Date startTime, Date endTime);
    
    /**
     * 归档历史数据
     */
    int archiveHistoryData(int days);
    
    /**
     * 清理过期历史数据
     */
    int cleanExpiredHistory(int days);
}
```

### 1.2 请求和响应DTO

```java
package com.knet.goods.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.goods.model.enums.HistoryOperationType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商品历史记录查询请求")
public class ProductHistoryQueryRequest extends BaseRequest {
    
    @Schema(description = "商品标识")
    private String listingId;
    
    @Schema(description = "原始商品ID")
    private Long originalId;
    
    @Schema(description = "操作类型")
    private HistoryOperationType operationType;
    
    @Schema(description = "操作人")
    private String operator;
    
    @Schema(description = "操作来源")
    private String operationSource;
    
    @Schema(description = "开始时间")
    private Date startTime;
    
    @Schema(description = "结束时间")
    private Date endTime;
    
    @Schema(description = "变更字段（模糊匹配）")
    private String changedField;
}
```

```java
package com.knet.goods.model.dto.resp;

import com.knet.goods.model.enums.HistoryOperationType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "商品历史记录查询响应")
public class ProductHistoryQueryResp {
    
    @Schema(description = "历史记录ID")
    private Long id;
    
    @Schema(description = "原始商品ID")
    private Long originalId;
    
    @Schema(description = "商品标识")
    private String listingId;
    
    @Schema(description = "操作类型")
    private HistoryOperationType operationType;
    
    @Schema(description = "操作时间")
    private Date operationTime;
    
    @Schema(description = "操作人")
    private String operator;
    
    @Schema(description = "操作来源")
    private String operationSource;
    
    @Schema(description = "变更前数据")
    private String beforeData;
    
    @Schema(description = "变更后数据")
    private String afterData;
    
    @Schema(description = "变更字段列表")
    private List<String> changedFields;
    
    @Schema(description = "操作备注")
    private String remark;
    
    @Schema(description = "创建时间")
    private Date createTime;
}
```

## 2. 服务实现

### 2.1 KnetProductHistoryServiceImpl

```java
package com.knet.goods.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.goods.mapper.KnetProductHistoryMapper;
import com.knet.goods.model.dto.message.ProductHistoryMessage;
import com.knet.goods.model.dto.req.ProductHistoryQueryRequest;
import com.knet.goods.model.dto.resp.ProductHistoryQueryResp;
import com.knet.goods.model.entity.KnetProductHistory;
import com.knet.goods.model.enums.HistoryOperationType;
import com.knet.goods.service.IKnetProductHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KnetProductHistoryServiceImpl 
    extends ServiceImpl<KnetProductHistoryMapper, KnetProductHistory> 
    implements IKnetProductHistoryService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String HISTORY_MESSAGE_KEY_PREFIX = "history:msg:";
    private static final long MESSAGE_EXPIRE_TIME = 24; // 24小时过期
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveHistory(ProductHistoryMessage message) {
        // 生成消息唯一键用于幂等性检查
        String messageKey = generateMessageKey(message);
        
        // 检查消息是否已处理
        if (isMessageProcessed(messageKey)) {
            log.warn("消息已处理，跳过: listingId={}, operationType={}", 
                    message.getListingId(), message.getOperationType());
            return;
        }
        
        try {
            // 标记消息正在处理
            markMessageProcessing(messageKey);
            
            // 转换并保存历史记录
            KnetProductHistory history = convertToHistory(message);
            save(history);
            
            // 标记消息处理完成
            markMessageProcessed(messageKey);
            
            log.info("历史记录保存成功: listingId={}, operationType={}", 
                    message.getListingId(), message.getOperationType());
                    
        } catch (Exception e) {
            // 清除处理标记
            clearMessageProcessing(messageKey);
            log.error("历史记录保存失败: listingId={}", message.getListingId(), e);
            throw e;
        }
    }
    
    @Override
    public IPage<ProductHistoryQueryResp> queryHistoryPage(ProductHistoryQueryRequest request) {
        Page<KnetProductHistory> page = new Page<>(request.getQueryStartPage(), request.getPageSize());
        
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = buildQueryWrapper(request);
        queryWrapper.orderByDesc(KnetProductHistory::getOperationTime);
        
        IPage<KnetProductHistory> historyPage = page(page, queryWrapper);
        
        return historyPage.convert(this::convertToResp);
    }
    
    @Override
    public List<ProductHistoryQueryResp> queryByListingId(String listingId) {
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnetProductHistory::getListingId, listingId)
                   .orderByDesc(KnetProductHistory::getOperationTime);
        
        List<KnetProductHistory> histories = list(queryWrapper);
        return histories.stream()
                       .map(this::convertToResp)
                       .collect(Collectors.toList());
    }
    
    @Override
    public List<ProductHistoryQueryResp> queryByOperationType(HistoryOperationType operationType, 
                                                            Date startTime, Date endTime) {
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnetProductHistory::getOperationType, operationType);
        
        if (startTime != null) {
            queryWrapper.ge(KnetProductHistory::getOperationTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(KnetProductHistory::getOperationTime, endTime);
        }
        
        queryWrapper.orderByDesc(KnetProductHistory::getOperationTime);
        
        List<KnetProductHistory> histories = list(queryWrapper);
        return histories.stream()
                       .map(this::convertToResp)
                       .collect(Collectors.toList());
    }
    
    @Override
    public List<ProductHistoryQueryResp> queryByOperator(String operator, Date startTime, Date endTime) {
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnetProductHistory::getOperator, operator);
        
        if (startTime != null) {
            queryWrapper.ge(KnetProductHistory::getOperationTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(KnetProductHistory::getOperationTime, endTime);
        }
        
        queryWrapper.orderByDesc(KnetProductHistory::getOperationTime);
        
        List<KnetProductHistory> histories = list(queryWrapper);
        return histories.stream()
                       .map(this::convertToResp)
                       .collect(Collectors.toList());
    }
    
    @Override
    public Map<String, Object> getHistoryStatistics(Date startTime, Date endTime) {
        return baseMapper.getHistoryStatistics(startTime, endTime);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int archiveHistoryData(int days) {
        Date archiveDate = new Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L);
        return baseMapper.archiveHistoryData(archiveDate);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanExpiredHistory(int days) {
        Date expireDate = new Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L);
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(KnetProductHistory::getCreateTime, expireDate);
        return baseMapper.delete(queryWrapper);
    }
    
    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<KnetProductHistory> buildQueryWrapper(ProductHistoryQueryRequest request) {
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(request.getListingId())) {
            queryWrapper.eq(KnetProductHistory::getListingId, request.getListingId());
        }
        
        if (request.getOriginalId() != null) {
            queryWrapper.eq(KnetProductHistory::getOriginalId, request.getOriginalId());
        }
        
        if (request.getOperationType() != null) {
            queryWrapper.eq(KnetProductHistory::getOperationType, request.getOperationType());
        }
        
        if (StringUtils.hasText(request.getOperator())) {
            queryWrapper.eq(KnetProductHistory::getOperator, request.getOperator());
        }
        
        if (StringUtils.hasText(request.getOperationSource())) {
            queryWrapper.eq(KnetProductHistory::getOperationSource, request.getOperationSource());
        }
        
        if (request.getStartTime() != null) {
            queryWrapper.ge(KnetProductHistory::getOperationTime, request.getStartTime());
        }
        
        if (request.getEndTime() != null) {
            queryWrapper.le(KnetProductHistory::getOperationTime, request.getEndTime());
        }
        
        if (StringUtils.hasText(request.getChangedField())) {
            queryWrapper.like(KnetProductHistory::getChangedFields, request.getChangedField());
        }
        
        return queryWrapper;
    }
    
    /**
     * 转换为响应对象
     */
    private ProductHistoryQueryResp convertToResp(KnetProductHistory history) {
        List<String> changedFields = new ArrayList<>();
        if (StringUtils.hasText(history.getChangedFields())) {
            changedFields = Arrays.asList(history.getChangedFields().split(","));
        }
        
        return ProductHistoryQueryResp.builder()
                .id(history.getId())
                .originalId(history.getOriginalId())
                .listingId(history.getListingId())
                .operationType(history.getOperationType())
                .operationTime(history.getOperationTime())
                .operator(history.getOperator())
                .operationSource(history.getOperationSource())
                .beforeData(history.getBeforeData())
                .afterData(history.getAfterData())
                .changedFields(changedFields)
                .remark(history.getRemark())
                .createTime(history.getCreateTime())
                .build();
    }
    
    /**
     * 消息转换为历史记录实体
     */
    private KnetProductHistory convertToHistory(ProductHistoryMessage message) {
        return KnetProductHistory.builder()
                .originalId(message.getOriginalId())
                .listingId(message.getListingId())
                .operationType(message.getOperationType())
                .operationTime(message.getOperationTime())
                .operator(message.getOperator())
                .operationSource(message.getOperationSource())
                .beforeData(message.getBeforeData())
                .afterData(message.getAfterData())
                .changedFields(message.getChangedFields() != null ? 
                              String.join(",", message.getChangedFields()) : null)
                .remark(message.getRemark())
                .build();
    }
    
    /**
     * 生成消息唯一键
     */
    private String generateMessageKey(ProductHistoryMessage message) {
        return HISTORY_MESSAGE_KEY_PREFIX + 
               message.getListingId() + ":" + 
               message.getOperationType() + ":" + 
               message.getOperationTime().getTime();
    }
    
    /**
     * 检查消息是否已处理
     */
    private boolean isMessageProcessed(String messageKey) {
        return redisTemplate.hasKey(messageKey);
    }
    
    /**
     * 标记消息正在处理
     */
    private void markMessageProcessing(String messageKey) {
        redisTemplate.opsForValue().set(messageKey, "PROCESSING", MESSAGE_EXPIRE_TIME, TimeUnit.HOURS);
    }
    
    /**
     * 标记消息处理完成
     */
    private void markMessageProcessed(String messageKey) {
        redisTemplate.opsForValue().set(messageKey, "PROCESSED", MESSAGE_EXPIRE_TIME, TimeUnit.HOURS);
    }
    
    /**
     * 清除消息处理标记
     */
    private void clearMessageProcessing(String messageKey) {
        redisTemplate.delete(messageKey);
    }
}
```

## 3. 数据访问层

### 3.1 KnetProductHistoryMapper接口

```java
package com.knet.goods.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.goods.model.entity.KnetProductHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.Map;

@Mapper
public interface KnetProductHistoryMapper extends BaseMapper<KnetProductHistory> {
    
    /**
     * 获取历史记录统计信息
     */
    Map<String, Object> getHistoryStatistics(@Param("startTime") Date startTime, 
                                            @Param("endTime") Date endTime);
    
    /**
     * 归档历史数据
     */
    int archiveHistoryData(@Param("archiveDate") Date archiveDate);
    
    /**
     * 创建历史记录表
     */
    void createHistoryTable();
    
    /**
     * 创建归档表
     */
    void createArchiveTable();
}
```

### 3.2 KnetProductHistoryMapper.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.goods.mapper.KnetProductHistoryMapper">

    <!-- 获取历史记录统计信息 -->
    <select id="getHistoryStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as total_records,
            COUNT(DISTINCT listing_id) as affected_products,
            COUNT(DISTINCT operator) as operators_count,
            operation_type,
            COUNT(*) as type_count
        FROM knet_product_history
        WHERE del_flag = 0
        <if test="startTime != null">
            AND operation_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND operation_time <= #{endTime}
        </if>
        GROUP BY operation_type

        UNION ALL

        SELECT
            COUNT(*) as total_records,
            COUNT(DISTINCT listing_id) as affected_products,
            COUNT(DISTINCT operator) as operators_count,
            'TOTAL' as operation_type,
            COUNT(*) as type_count
        FROM knet_product_history
        WHERE del_flag = 0
        <if test="startTime != null">
            AND operation_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND operation_time <= #{endTime}
        </if>
    </select>

    <!-- 归档历史数据 -->
    <insert id="archiveHistoryData">
        INSERT INTO knet_product_history_archive
        (original_id, listing_id, operation_type, operation_time, operator, operation_source,
         before_data, after_data, changed_fields, remark, create_time, update_time, del_flag, version,
         archive_time, archive_reason)
        SELECT
            original_id, listing_id, operation_type, operation_time, operator, operation_source,
            before_data, after_data, changed_fields, remark, create_time, update_time, del_flag, version,
            NOW() as archive_time,
            CONCAT('自动归档-超过指定天数') as archive_reason
        FROM knet_product_history
        WHERE create_time < #{archiveDate}
          AND del_flag = 0;

        DELETE FROM knet_product_history
        WHERE create_time < #{archiveDate}
          AND del_flag = 0;
    </insert>

    <!-- 创建历史记录表 -->
    <update id="createHistoryTable">
        CREATE TABLE IF NOT EXISTS `knet_product_history` (
            `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `original_id` BIGINT NOT NULL COMMENT '原始商品记录ID',
            `listing_id` VARCHAR(100) NOT NULL COMMENT '商品唯一标识符',
            `operation_type` VARCHAR(20) NOT NULL COMMENT '操作类型',
            `operation_time` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '操作时间',
            `operator` VARCHAR(100) DEFAULT NULL COMMENT '操作人',
            `operation_source` VARCHAR(100) DEFAULT NULL COMMENT '操作来源',
            `before_data` JSON DEFAULT NULL COMMENT '变更前数据',
            `after_data` JSON DEFAULT NULL COMMENT '变更后数据',
            `changed_fields` TEXT DEFAULT NULL COMMENT '变更字段列表',
            `remark` VARCHAR(500) DEFAULT NULL COMMENT '操作备注',
            `create_time` TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
            `update_time` TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录更新时间',
            `del_flag` TINYINT DEFAULT 0 COMMENT '删除标志',
            `version` INT DEFAULT 1 COMMENT '版本号',
            PRIMARY KEY (`id`),
            INDEX `idx_original_id` (`original_id`),
            INDEX `idx_listing_id` (`listing_id`),
            INDEX `idx_operation_type` (`operation_type`),
            INDEX `idx_operation_time` (`operation_time`),
            INDEX `idx_operator` (`operator`),
            INDEX `idx_create_time` (`create_time`),
            INDEX `idx_listing_operation` (`listing_id`, `operation_type`, `operation_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品历史记录表';
    </update>

    <!-- 创建归档表 -->
    <update id="createArchiveTable">
        CREATE TABLE IF NOT EXISTS `knet_product_history_archive` (
            `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `original_id` BIGINT NOT NULL COMMENT '原始商品记录ID',
            `listing_id` VARCHAR(100) NOT NULL COMMENT '商品唯一标识符',
            `operation_type` VARCHAR(20) NOT NULL COMMENT '操作类型',
            `operation_time` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '操作时间',
            `operator` VARCHAR(100) DEFAULT NULL COMMENT '操作人',
            `operation_source` VARCHAR(100) DEFAULT NULL COMMENT '操作来源',
            `before_data` JSON DEFAULT NULL COMMENT '变更前数据',
            `after_data` JSON DEFAULT NULL COMMENT '变更后数据',
            `changed_fields` TEXT DEFAULT NULL COMMENT '变更字段列表',
            `remark` VARCHAR(500) DEFAULT NULL COMMENT '操作备注',
            `create_time` TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
            `update_time` TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录更新时间',
            `del_flag` TINYINT DEFAULT 0 COMMENT '删除标志',
            `version` INT DEFAULT 1 COMMENT '版本号',
            `archive_time` TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '归档时间',
            `archive_reason` VARCHAR(200) DEFAULT NULL COMMENT '归档原因',
            PRIMARY KEY (`id`),
            INDEX `idx_original_id` (`original_id`),
            INDEX `idx_listing_id` (`listing_id`),
            INDEX `idx_operation_time` (`operation_time`),
            INDEX `idx_archive_time` (`archive_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品历史记录归档表';
    </update>

</mapper>
```

## 4. Controller接口实现

### 4.1 ProductHistoryController

```java
package com.knet.goods.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.constant.SystemConstant;
import com.knet.common.result.HttpResult;
import com.knet.goods.model.dto.req.ProductHistoryQueryRequest;
import com.knet.goods.model.dto.resp.ProductHistoryQueryResp;
import com.knet.goods.model.enums.HistoryOperationType;
import com.knet.goods.service.IKnetProductHistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/product/history")
@Tag(name = "商品历史记录控制器", description = "商品历史记录查询和管理")
public class ProductHistoryController {

    @Autowired
    private IKnetProductHistoryService historyService;

    /**
     * 分页查询历史记录
     */
    @Loggable(value = "分页查询商品历史记录")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(description = "分页查询商品历史记录")
    @PostMapping("/page")
    public HttpResult<IPage<ProductHistoryQueryResp>> queryHistoryPage(
            @Validated @RequestBody ProductHistoryQueryRequest request) {
        return HttpResult.ok(historyService.queryHistoryPage(request));
    }

    /**
     * 查询特定商品的历史记录
     */
    @Loggable(value = "查询商品历史记录")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(description = "查询特定商品的历史记录")
    @GetMapping("/listing/{listingId}")
    public HttpResult<List<ProductHistoryQueryResp>> queryByListingId(
            @Parameter(description = "商品标识") @PathVariable String listingId) {
        return HttpResult.ok(historyService.queryByListingId(listingId));
    }

    /**
     * 查询特定操作类型的历史记录
     */
    @Loggable(value = "按操作类型查询历史记录")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(description = "查询特定操作类型的历史记录")
    @GetMapping("/operation/{operationType}")
    public HttpResult<List<ProductHistoryQueryResp>> queryByOperationType(
            @Parameter(description = "操作类型") @PathVariable HistoryOperationType operationType,
            @Parameter(description = "开始时间") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return HttpResult.ok(historyService.queryByOperationType(operationType, startTime, endTime));
    }

    /**
     * 查询特定操作人的历史记录
     */
    @Loggable(value = "按操作人查询历史记录")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(description = "查询特定操作人的历史记录")
    @GetMapping("/operator/{operator}")
    public HttpResult<List<ProductHistoryQueryResp>> queryByOperator(
            @Parameter(description = "操作人") @PathVariable String operator,
            @Parameter(description = "开始时间") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return HttpResult.ok(historyService.queryByOperator(operator, startTime, endTime));
    }

    /**
     * 获取历史记录统计信息
     */
    @Loggable(value = "获取历史记录统计")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(description = "获取历史记录统计信息")
    @GetMapping("/statistics")
    public HttpResult<Map<String, Object>> getHistoryStatistics(
            @Parameter(description = "开始时间") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return HttpResult.ok(historyService.getHistoryStatistics(startTime, endTime));
    }
}
```

### 4.2 管理员接口 - ProductHistoryAdminController

```java
package com.knet.goods.controller.admin;

import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.annotation.PermissionCheck;
import com.knet.common.constant.SystemConstant;
import com.knet.common.result.HttpResult;
import com.knet.goods.service.IKnetProductHistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/admin/product/history")
@Tag(name = "商品历史记录管理接口", description = "管理员专用的历史记录管理功能")
public class ProductHistoryAdminController {

    @Autowired
    private IKnetProductHistoryService historyService;

    /**
     * 归档历史数据
     */
    @Loggable(value = "归档商品历史数据")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @PermissionCheck(permission = "admin")
    @Operation(description = "归档超过指定天数的历史数据")
    @PostMapping("/archive")
    public HttpResult<Integer> archiveHistoryData(
            @Parameter(description = "归档天数，默认365天") @RequestParam(defaultValue = "365") int days) {
        int archivedCount = historyService.archiveHistoryData(days);
        return HttpResult.ok(archivedCount);
    }

    /**
     * 清理过期历史数据
     */
    @Loggable(value = "清理过期历史数据")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @PermissionCheck(permission = "admin")
    @Operation(description = "清理超过指定天数的历史数据")
    @DeleteMapping("/clean")
    public HttpResult<Integer> cleanExpiredHistory(
            @Parameter(description = "清理天数，默认1095天(3年)") @RequestParam(defaultValue = "1095") int days) {
        int cleanedCount = historyService.cleanExpiredHistory(days);
        return HttpResult.ok(cleanedCount);
    }

    /**
     * 初始化历史记录表
     */
    @Loggable(value = "初始化历史记录表")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @PermissionCheck(permission = "admin")
    @Operation(description = "初始化历史记录相关表结构")
    @PostMapping("/init")
    public HttpResult<String> initHistoryTables() {
        try {
            historyService.getBaseMapper().createHistoryTable();
            historyService.getBaseMapper().createArchiveTable();
            return HttpResult.ok("历史记录表初始化成功");
        } catch (Exception e) {
            log.error("初始化历史记录表失败", e);
            return HttpResult.error("初始化失败: " + e.getMessage());
        }
    }
}
```

## 5. 定时任务实现

### 5.1 历史数据清理定时任务

```java
package com.knet.goods.job;

import com.knet.goods.service.IKnetProductHistoryService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ProductHistoryCleanupJob {

    @Autowired
    private IKnetProductHistoryService historyService;

    /**
     * 历史数据归档任务
     * 参数格式: archiveDays=365
     */
    @XxlJob("productHistoryArchiveJob")
    public void archiveHistoryData() {
        String param = XxlJobHelper.getJobParam();
        int archiveDays = 365; // 默认365天

        if (param != null && param.contains("archiveDays=")) {
            try {
                archiveDays = Integer.parseInt(param.split("archiveDays=")[1]);
            } catch (Exception e) {
                log.warn("解析归档天数参数失败，使用默认值: {}", archiveDays);
            }
        }

        try {
            int archivedCount = historyService.archiveHistoryData(archiveDays);
            log.info("历史数据归档完成，归档记录数: {}, 归档天数: {}", archivedCount, archiveDays);
            XxlJobHelper.handleSuccess("归档完成，记录数: " + archivedCount);
        } catch (Exception e) {
            log.error("历史数据归档失败", e);
            XxlJobHelper.handleFail("归档失败: " + e.getMessage());
        }
    }

    /**
     * 历史数据清理任务
     * 参数格式: cleanDays=1095
     */
    @XxlJob("productHistoryCleanupJob")
    public void cleanExpiredHistory() {
        String param = XxlJobHelper.getJobParam();
        int cleanDays = 1095; // 默认3年

        if (param != null && param.contains("cleanDays=")) {
            try {
                cleanDays = Integer.parseInt(param.split("cleanDays=")[1]);
            } catch (Exception e) {
                log.warn("解析清理天数参数失败，使用默认值: {}", cleanDays);
            }
        }

        try {
            int cleanedCount = historyService.cleanExpiredHistory(cleanDays);
            log.info("过期历史数据清理完成，清理记录数: {}, 清理天数: {}", cleanedCount, cleanDays);
            XxlJobHelper.handleSuccess("清理完成，记录数: " + cleanedCount);
        } catch (Exception e) {
            log.error("过期历史数据清理失败", e);
            XxlJobHelper.handleFail("清理失败: " + e.getMessage());
        }
    }
}
```

这个完整的服务实现提供了：

1. **完整的CRUD操作**: 支持历史记录的增删改查
2. **灵活的查询功能**: 支持多种条件组合查询
3. **幂等性保证**: 基于Redis的消息去重机制
4. **统计分析功能**: 提供历史数据统计信息
5. **数据归档功能**: 支持历史数据的归档和清理
6. **事务支持**: 确保数据一致性
7. **RESTful接口**: 提供完整的HTTP接口
8. **权限控制**: 管理员功能需要特殊权限
9. **定时任务**: 自动化的数据清理和归档

