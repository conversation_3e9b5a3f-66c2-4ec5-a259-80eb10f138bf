# 商品价格区间搜索功能

## 概述

为 `queryProductGroupBySku` 方法添加了价格区间搜索功能，支持根据策略价格进行商品筛选。前端传入的策略价格会自动转换为原始价格进行数据库查询，确保筛选结果的准确性。

## 新增参数

### ProductQueryRequest 新增字段

```java
@Schema(description = "价格区间最小值（策略价格，美元，需与maxPrice同时提供）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
private String minPrice;

@Schema(description = "价格区间最大值（策略价格，美元，需与minPrice同时提供）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
private String maxPrice;
```

## 价格转换逻辑

### 转换流程

1. **前端输入**：用户输入策略价格区间（美元）
2. **价格转换**：策略价格（美元）→ 策略价格（美分）→ 原始价格（美分）
3. **数据库查询**：使用原始价格进行 `MIN(kp.price) BETWEEN` 查询
4. **结果返回**：查询结果应用价格策略后返回给前端

### 转换示例

```
前端输入: minPrice="100.00", maxPrice="200.00"
↓
转换为美分: 10000, 20000
↓
移除价格策略: 9000, 18181 (假设策略为+10美元或+10%)
↓
数据库查询: MIN(kp.price) <= 18181 AND MAX(kp.price) >= 9000
↓
返回结果: 应用价格策略后的商品列表
```

### 价格区间重叠逻辑

查询逻辑使用区间重叠判断，确保能正确筛选出价格区间有交集的商品：

**示例场景**：
- 商品A：价格区间 [10, 100]
- 商品B：价格区间 [30, 1000]
- 查询区间：[20, 100]

**查询结果**：
- 商品A：MIN(10) <= 100 AND MAX(100) >= 20 ✅ 符合条件
- 商品B：MIN(30) <= 100 AND MAX(1000) >= 20 ✅ 符合条件

**查询区间：[20, 29]**
- 商品A：MIN(10) <= 29 AND MAX(100) >= 20 ✅ 符合条件
- 商品B：MIN(30) <= 29 ❌ 不符合条件

## 使用示例

### 1. 基础价格区间筛选

```json
{
  "pageNo": 1,
  "pageSize": 10,
  "minPrice": "100.00",
  "maxPrice": "200.00"
}
```

### 2. 组合筛选（价格 + 尺码）

```json
{
  "pageNo": 1,
  "pageSize": 10,
  "spec": "US 10",
  "minPrice": "150.00",
  "maxPrice": "300.00"
}
```

### 3. 全面筛选（价格 + 数量 + 品牌）

```json
{
  "pageNo": 1,
  "pageSize": 10,
  "brand": "Nike",
  "minTotal": 5,
  "maxTotal": 20,
  "minPrice": "80.00",
  "maxPrice": "250.00"
}
```

## 实现细节

### 价格转换方法

```java
private void convertStrategyPriceToOriginalPrice(ProductQueryRequest request) {
    if (StrUtil.isNotBlank(request.getMinPrice()) && StrUtil.isNotBlank(request.getMaxPrice())) {
        try {
            // 策略价格（美元）→ 美分
            Long minStrategyPriceCents = PriceFormatUtil.formatYuanToCents(request.getMinPrice());
            Long maxStrategyPriceCents = PriceFormatUtil.formatYuanToCents(request.getMaxPrice());
            
            // 策略价格 → 原始价格
            Long minOriginalPriceCents = pricingStrategyService.removePricingStrategy(minStrategyPriceCents);
            Long maxOriginalPriceCents = pricingStrategyService.removePricingStrategy(maxStrategyPriceCents);
            
            // 设置原始价格用于数据库查询
            request.setMinPrice(String.valueOf(minOriginalPriceCents));
            request.setMaxPrice(String.valueOf(maxOriginalPriceCents));
        } catch (Exception e) {
            // 转换失败时清空价格条件
            request.setMinPrice(null);
            request.setMaxPrice(null);
        }
    }
}
```

### SQL 查询优化

使用区间重叠判断逻辑，确保能正确筛选出价格区间有交集的商品：

```xml
<if test="request.minPrice != null and request.maxPrice != null">
    AND MIN(kp.price) &lt;= #{request.maxPrice} AND MAX(kp.price) &gt;= #{request.minPrice}
</if>
```

**查询逻辑说明**：
- `MIN(kp.price) <= 查询最大价格`：商品最低价不超过查询上限
- `MAX(kp.price) >= 查询最小价格`：商品最高价不低于查询下限
- 两个条件同时满足时，说明商品价格区间与查询区间有重叠

## 错误处理

1. **价格转换失败**：自动清空价格筛选条件，避免查询异常
2. **单独设置价格**：只有同时提供 `minPrice` 和 `maxPrice` 才会生效
3. **无效价格格式**：通过 try-catch 捕获并记录警告日志

## 性能考虑

1. **缓存机制**：价格转换结果会被 Redis 缓存 5 分钟
2. **批量处理**：支持批量价格转换，提高性能
3. **临时表优化**：价格筛选同样支持临时表优化查询

## 注意事项

1. **价格单位**：前端传入美元，数据库存储美分
2. **策略依赖**：依赖 `PricingStrategyService` 进行价格转换
3. **同时提供**：`minPrice` 和 `maxPrice` 必须同时提供才会生效
4. **精度处理**：价格转换过程中会进行适当的四舍五入处理

## 支持的查询方式

- ✅ 传统查询方式
- ✅ 临时表优化查询
- ✅ 分页查询
- ✅ 排序功能
- ✅ 组合筛选条件
