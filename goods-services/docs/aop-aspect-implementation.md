# AOP切面实现 - 商品历史记录

## 1. 核心切面实现

### 1.1 ProductHistoryAspect主切面

```java
package com.knet.goods.aspect;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.knet.common.context.ApiKeyContext;
import com.knet.goods.model.dto.message.ProductHistoryMessage;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.model.enums.HistoryOperationType;
import com.knet.goods.service.IKnetProductService;
import com.knet.goods.service.message.ProductHistoryMessageSender;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Aspect
@Component
@Slf4j
public class ProductHistoryAspect {
    
    @Autowired
    private ProductHistoryMessageSender messageSender;
    
    @Autowired
    private IKnetProductService productService;
    
    /**
     * 拦截所有KnetProduct相关的增删改操作
     */
    @Around("execution(* com.knet.goods.service.impl.KnetProductServiceImpl.save*(..)) || " +
            "execution(* com.knet.goods.service.impl.KnetProductServiceImpl.update*(..)) || " +
            "execution(* com.knet.goods.service.impl.KnetProductServiceImpl.remove*(..)) || " +
            "execution(* com.knet.goods.service.impl.KnetProductServiceImpl.insert*(..)) || " +
            "execution(* com.knet.goods.service.impl.KnetProductServiceImpl.create*(..)) || " +
            "execution(* com.knet.goods.service.impl.KnetProductServiceImpl.process*(..))")
    public Object recordHistory(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String methodName = method.getName();
        Object[] args = joinPoint.getArgs();
        
        log.debug("拦截到方法调用: {}", methodName);
        
        // 确定操作类型
        HistoryOperationType operationType = determineOperationType(methodName);
        if (operationType == null) {
            // 不需要记录历史的方法，直接执行
            return joinPoint.proceed();
        }
        
        // 获取变更前的数据
        Map<String, KnetProduct> beforeDataMap = getBeforeData(methodName, args);
        
        // 执行原方法
        Object result = joinPoint.proceed();
        
        // 异步发送历史记录消息
        try {
            sendHistoryMessages(operationType, methodName, args, result, beforeDataMap);
        } catch (Exception e) {
            log.error("发送历史记录消息失败，但不影响主业务", e);
        }
        
        return result;
    }
    
    /**
     * 根据方法名确定操作类型
     */
    private HistoryOperationType determineOperationType(String methodName) {
        if (methodName.startsWith("save") || methodName.startsWith("create")) {
            return HistoryOperationType.CREATE;
        } else if (methodName.startsWith("update") || methodName.startsWith("process")) {
            return HistoryOperationType.UPDATE;
        } else if (methodName.startsWith("remove") || methodName.startsWith("delete")) {
            return HistoryOperationType.DELETE;
        } else if (methodName.contains("Batch") && methodName.startsWith("insert")) {
            return HistoryOperationType.BATCH_INSERT;
        } else if (methodName.contains("Batch") && methodName.startsWith("update")) {
            return HistoryOperationType.BATCH_UPDATE;
        }
        return null;
    }
    
    /**
     * 获取变更前的数据
     */
    private Map<String, KnetProduct> getBeforeData(String methodName, Object[] args) {
        Map<String, KnetProduct> beforeDataMap = new HashMap<>();
        
        try {
            if (methodName.startsWith("update") || methodName.startsWith("process")) {
                // 更新操作需要获取变更前的数据
                Set<String> listingIds = extractListingIds(methodName, args);
                if (!listingIds.isEmpty()) {
                    List<KnetProduct> beforeProducts = productService.list(
                        new LambdaQueryWrapper<KnetProduct>()
                            .in(KnetProduct::getListingId, listingIds)
                    );
                    beforeDataMap = beforeProducts.stream()
                        .collect(Collectors.toMap(KnetProduct::getListingId, p -> p));
                }
            } else if (methodName.startsWith("remove") || methodName.startsWith("delete")) {
                // 删除操作需要获取被删除的数据
                Set<String> listingIds = extractListingIds(methodName, args);
                if (!listingIds.isEmpty()) {
                    List<KnetProduct> beforeProducts = productService.list(
                        new LambdaQueryWrapper<KnetProduct>()
                            .in(KnetProduct::getListingId, listingIds)
                    );
                    beforeDataMap = beforeProducts.stream()
                        .collect(Collectors.toMap(KnetProduct::getListingId, p -> p));
                }
            }
        } catch (Exception e) {
            log.warn("获取变更前数据失败: methodName={}", methodName, e);
        }
        
        return beforeDataMap;
    }
    
    /**
     * 从方法参数中提取listingId
     */
    private Set<String> extractListingIds(String methodName, Object[] args) {
        Set<String> listingIds = new HashSet<>();
        
        for (Object arg : args) {
            if (arg instanceof KnetProduct) {
                KnetProduct product = (KnetProduct) arg;
                if (product.getListingId() != null) {
                    listingIds.add(product.getListingId());
                }
            } else if (arg instanceof List) {
                List<?> list = (List<?>) arg;
                for (Object item : list) {
                    if (item instanceof KnetProduct) {
                        KnetProduct product = (KnetProduct) item;
                        if (product.getListingId() != null) {
                            listingIds.add(product.getListingId());
                        }
                    } else if (item instanceof String) {
                        listingIds.add((String) item);
                    }
                }
            } else if (arg instanceof String && isListingId((String) arg)) {
                listingIds.add((String) arg);
            }
        }
        
        return listingIds;
    }
    
    /**
     * 判断字符串是否为listingId格式
     */
    private boolean isListingId(String str) {
        // 根据实际的listingId格式进行判断
        return str != null && str.startsWith("KNET_");
    }
    
    /**
     * 发送历史记录消息
     */
    private void sendHistoryMessages(HistoryOperationType operationType, 
                                   String methodName, 
                                   Object[] args, 
                                   Object result,
                                   Map<String, KnetProduct> beforeDataMap) {
        
        List<ProductHistoryMessage> messages = buildHistoryMessages(
            operationType, methodName, args, result, beforeDataMap);
        
        for (ProductHistoryMessage message : messages) {
            messageSender.sendHistoryMessage(message);
        }
    }
    
    /**
     * 构建历史记录消息
     */
    private List<ProductHistoryMessage> buildHistoryMessages(HistoryOperationType operationType,
                                                           String methodName,
                                                           Object[] args,
                                                           Object result,
                                                           Map<String, KnetProduct> beforeDataMap) {
        List<ProductHistoryMessage> messages = new ArrayList<>();
        Date operationTime = new Date();
        String operator = getOperator();
        String operationSource = "service_layer";
        String remark = buildRemark(methodName, args);
        
        switch (operationType) {
            case CREATE:
                messages.addAll(buildCreateMessages(args, operationTime, operator, operationSource, remark));
                break;
            case UPDATE:
                messages.addAll(buildUpdateMessages(args, beforeDataMap, operationTime, operator, operationSource, remark));
                break;
            case DELETE:
                messages.addAll(buildDeleteMessages(beforeDataMap, operationTime, operator, operationSource, remark));
                break;
            case BATCH_INSERT:
                messages.addAll(buildBatchInsertMessages(args, operationTime, operator, operationSource, remark));
                break;
            default:
                log.warn("未处理的操作类型: {}", operationType);
        }
        
        return messages;
    }
    
    /**
     * 构建创建操作的历史消息
     */
    private List<ProductHistoryMessage> buildCreateMessages(Object[] args, 
                                                          Date operationTime, 
                                                          String operator, 
                                                          String operationSource, 
                                                          String remark) {
        List<ProductHistoryMessage> messages = new ArrayList<>();
        
        for (Object arg : args) {
            if (arg instanceof KnetProduct) {
                KnetProduct product = (KnetProduct) arg;
                ProductHistoryMessage message = ProductHistoryMessage.builder()
                    .originalId(product.getId())
                    .listingId(product.getListingId())
                    .operationType(HistoryOperationType.CREATE)
                    .operationTime(operationTime)
                    .operator(operator)
                    .operationSource(operationSource)
                    .beforeData(null) // 创建操作没有变更前数据
                    .afterData(JSON.toJSONString(product))
                    .changedFields(getAllFields())
                    .remark(remark)
                    .build();
                messages.add(message);
            }
        }
        
        return messages;
    }
    
    /**
     * 构建更新操作的历史消息
     */
    private List<ProductHistoryMessage> buildUpdateMessages(Object[] args,
                                                          Map<String, KnetProduct> beforeDataMap,
                                                          Date operationTime,
                                                          String operator,
                                                          String operationSource,
                                                          String remark) {
        List<ProductHistoryMessage> messages = new ArrayList<>();
        
        // 获取更新后的数据
        Set<String> listingIds = extractListingIds("update", args);
        if (!listingIds.isEmpty()) {
            List<KnetProduct> afterProducts = productService.list(
                new LambdaQueryWrapper<KnetProduct>()
                    .in(KnetProduct::getListingId, listingIds)
            );
            
            for (KnetProduct afterProduct : afterProducts) {
                KnetProduct beforeProduct = beforeDataMap.get(afterProduct.getListingId());
                if (beforeProduct != null) {
                    List<String> changedFields = getChangedFields(beforeProduct, afterProduct);
                    if (!changedFields.isEmpty()) {
                        ProductHistoryMessage message = ProductHistoryMessage.builder()
                            .originalId(afterProduct.getId())
                            .listingId(afterProduct.getListingId())
                            .operationType(HistoryOperationType.UPDATE)
                            .operationTime(operationTime)
                            .operator(operator)
                            .operationSource(operationSource)
                            .beforeData(JSON.toJSONString(beforeProduct))
                            .afterData(JSON.toJSONString(afterProduct))
                            .changedFields(changedFields)
                            .remark(remark)
                            .build();
                        messages.add(message);
                    }
                }
            }
        }
        
        return messages;
    }
    
    /**
     * 构建删除操作的历史消息
     */
    private List<ProductHistoryMessage> buildDeleteMessages(Map<String, KnetProduct> beforeDataMap,
                                                          Date operationTime,
                                                          String operator,
                                                          String operationSource,
                                                          String remark) {
        List<ProductHistoryMessage> messages = new ArrayList<>();
        
        for (KnetProduct beforeProduct : beforeDataMap.values()) {
            ProductHistoryMessage message = ProductHistoryMessage.builder()
                .originalId(beforeProduct.getId())
                .listingId(beforeProduct.getListingId())
                .operationType(HistoryOperationType.DELETE)
                .operationTime(operationTime)
                .operator(operator)
                .operationSource(operationSource)
                .beforeData(JSON.toJSONString(beforeProduct))
                .afterData(null) // 删除操作没有变更后数据
                .changedFields(Arrays.asList("delFlag"))
                .remark(remark)
                .build();
            messages.add(message);
        }
        
        return messages;
    }
    
    /**
     * 构建批量插入操作的历史消息
     */
    private List<ProductHistoryMessage> buildBatchInsertMessages(Object[] args,
                                                               Date operationTime,
                                                               String operator,
                                                               String operationSource,
                                                               String remark) {
        List<ProductHistoryMessage> messages = new ArrayList<>();
        
        for (Object arg : args) {
            if (arg instanceof List) {
                List<?> list = (List<?>) arg;
                for (Object item : list) {
                    if (item instanceof KnetProduct) {
                        KnetProduct product = (KnetProduct) item;
                        ProductHistoryMessage message = ProductHistoryMessage.builder()
                            .originalId(product.getId())
                            .listingId(product.getListingId())
                            .operationType(HistoryOperationType.BATCH_INSERT)
                            .operationTime(operationTime)
                            .operator(operator)
                            .operationSource(operationSource)
                            .beforeData(null)
                            .afterData(JSON.toJSONString(product))
                            .changedFields(getAllFields())
                            .remark(remark)
                            .build();
                        messages.add(message);
                    }
                }
            }
        }
        
        return messages;
    }
    
    /**
     * 获取操作人
     */
    private String getOperator() {
        try {
            return ApiKeyContext.getApiKey();
        } catch (Exception e) {
            return "system";
        }
    }
    
    /**
     * 构建操作备注
     */
    private String buildRemark(String methodName, Object[] args) {
        return String.format("方法调用: %s, 参数数量: %d", methodName, args.length);
    }
    
    /**
     * 获取所有字段列表
     */
    private List<String> getAllFields() {
        return Arrays.asList("listingId", "oneId", "sku", "spec", "brand", "remarks", 
                           "price", "stock", "warehouse", "status", "mark", "creator", "source");
    }
    
    /**
     * 比较两个对象，获取变更的字段
     */
    private List<String> getChangedFields(KnetProduct before, KnetProduct after) {
        List<String> changedFields = new ArrayList<>();
        
        if (!Objects.equals(before.getPrice(), after.getPrice())) {
            changedFields.add("price");
        }
        if (!Objects.equals(before.getStock(), after.getStock())) {
            changedFields.add("stock");
        }
        if (!Objects.equals(before.getStatus(), after.getStatus())) {
            changedFields.add("status");
        }
        if (!Objects.equals(before.getMark(), after.getMark())) {
            changedFields.add("mark");
        }
        if (!Objects.equals(before.getWarehouse(), after.getWarehouse())) {
            changedFields.add("warehouse");
        }
        if (!Objects.equals(before.getBrand(), after.getBrand())) {
            changedFields.add("brand");
        }
        if (!Objects.equals(before.getRemarks(), after.getRemarks())) {
            changedFields.add("remarks");
        }
        if (!Objects.equals(before.getSpec(), after.getSpec())) {
            changedFields.add("spec");
        }
        
        return changedFields;
    }
}
```

## 2. 切面配置和优化

### 2.1 切面配置类

```java
package com.knet.goods.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class AspectConfig {
    // AOP配置
}
```

### 2.2 性能优化配置

```java
package com.knet.goods.aspect;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "knet.history.aspect")
public class HistoryAspectProperties {
    
    /**
     * 是否启用历史记录
     */
    private boolean enabled = true;
    
    /**
     * 是否异步处理
     */
    private boolean async = true;
    
    /**
     * 批量处理大小
     */
    private int batchSize = 100;
    
    /**
     * 最大重试次数
     */
    private int maxRetries = 3;
    
    // getters and setters
}
```

这个AOP切面实现提供了：

1. **全面的方法拦截**: 覆盖所有KnetProduct的增删改操作
2. **智能的数据变更检测**: 自动识别变更前后的数据差异
3. **灵活的消息构建**: 根据不同操作类型构建相应的历史消息
4. **性能优化**: 异步处理，不影响主业务性能
5. **错误处理**: 历史记录失败不影响主业务执行

这确保了所有数据变动都能被准确记录到历史表中。
