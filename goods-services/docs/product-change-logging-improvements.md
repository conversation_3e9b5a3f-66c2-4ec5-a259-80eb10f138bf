# KnetProduct模型变动日志改进方案

## 改进概述

本次改进主要解决了三个问题：
1. 去掉了OptimizedLoggableAspect切面对请求响应日志的超长字符截断
2. 检查并完善了KnetProduct模型数据变化时的日志记录
3. 形成了完善的KnetProduct模型变动日志追踪体系

## 具体改进内容

### 1. 日志截断限制调整

**文件**: `common/src/main/java/com/knet/common/utils/LogSanitizer.java`

**改动**:
```java
// 修改前
private static final int MAX_ARGS_LENGTH = 1000;
private static final int MAX_RESULT_LENGTH = 1000;

// 修改后  
private static final int MAX_ARGS_LENGTH = 50000;
private static final int MAX_RESULT_LENGTH = 50000;
```

**效果**: 现在可以打印完整的请求响应日志，不会被截断，便于问题排查和数据追踪。

### 2. KnetProduct变动日志工具类

**新增文件**: `goods-services/src/main/java/com/knet/goods/utils/ProductChangeLogger.java`

**功能特性**:
- 🆕 **商品创建日志**: 记录新商品的完整信息
- 🆕 **批量商品创建日志**: 记录批量操作的详细信息
- 💰 **价格更新日志**: 记录价格变更的前后对比
- 📴 **商品下架日志**: 记录下架商品的详细信息
- 🏷️ **标识更新日志**: 记录商品标识变更和分布统计
- 🔒 **库存锁定日志**: 记录库存锁定的详细过程
- 🔄 **状态变更日志**: 记录商品状态变化
- 🗑️ **商品删除日志**: 记录删除操作
- ✅/❌ **操作结果日志**: 记录操作成功/失败
- 🔍 **数据对比日志**: 记录变更前后的数据对比

### 3. Service层方法日志增强

**文件**: `goods-services/src/main/java/com/knet/goods/service/impl/KnetProductServiceImpl.java`

#### 3.1 商品创建方法 (`createByKnet`)
- ✅ 添加了完整的商品创建日志
- 📋 记录所有关键字段：listingId, oneId, sku, spec, 品牌, 价格, 库存, 仓库, 状态, 标识, 来源
- 👤 记录操作人信息

#### 3.2 批量插入方法 (`insertIgnoreBatch`)
- ✅ 添加了批量操作开始和完成日志
- 📦 记录前5个商品的详细信息（避免日志过长）
- 📊 统计总数量和操作结果
- ⚠️ 处理空列表的边界情况

#### 3.3 商品下架方法 (`updateKnetProductForOffSale`)
- ✅ 记录下架操作的完整流程
- 📋 获取并记录变更前的商品状态
- 📦 记录每个被下架商品的详细信息
- 📊 统计请求数量vs实际影响数量

#### 3.4 价格更新方法 (`processKnetProductPrice`)
- ✅ 记录价格变更的前后对比
- 💰 详细记录：listingId, oneId, sku, spec, 价格变更, 币种
- ⚠️ 处理商品不存在的情况
- 🔒 记录并发更新失败的情况

#### 3.5 商品标识更新方法 (`setProductModifyMark`)
- ✅ 记录批量标识更新操作
- 📊 统计变更前的标识分布情况
- 📦 记录前10个SKU的详细信息
- 📈 对比更新前后的数据

#### 3.6 库存锁定方法 (`lockInventory`)
- ✅ 记录库存锁定的完整流程
- 🔒 详细记录：SKU, 规格, 价格, 数量, 锁定的商品ID
- ❌ 记录库存不足的详细信息
- 📊 统计锁定成功率

## 日志格式标准化

### 日志标识符说明
- 🆕 `[PRODUCT_CREATE]` - 商品创建
- 🔄 `[BATCH_PRODUCT_CREATE]` - 批量商品创建  
- 💰 `[PRICE_UPDATE]` - 价格更新
- 📴 `[PRODUCT_OFF_SALE]` - 商品下架
- 🏷️ `[MARK_UPDATE]` - 标识更新
- 🔒 `[INVENTORY_LOCK]` - 库存锁定
- ❌ `[INVENTORY_LOCK_FAILED]` - 库存锁定失败
- 🔄 `[STATUS_CHANGE]` - 状态变更
- 🗑️ `[PRODUCT_DELETE]` - 商品删除
- ✅ `[OPERATION_SUCCESS]` - 操作成功
- ❌ `[OPERATION_FAILED]` - 操作失败
- 🔍 `[DATA_COMPARISON]` - 数据对比

### 日志字段标准
每条变动日志都包含以下标准字段：
- **操作人**: 通过ApiKeyContext获取
- **操作时间**: 精确到毫秒
- **商品标识**: listingId, oneId, sku, spec
- **变更内容**: 具体的变更字段和值
- **操作结果**: 成功/失败状态

## 追踪能力提升

### 1. 完整的操作链路追踪
- 每个操作都有唯一的请求ID（通过@Loggable注解）
- 操作人信息完整记录
- 操作时间精确记录
- 操作结果明确标识

### 2. 数据变更前后对比
- 价格更新：记录变更前后的价格对比
- 状态变更：记录状态变化轨迹
- 标识更新：记录标识分布变化
- 库存变动：记录库存锁定详情

### 3. 业务场景覆盖
- ✅ 商品创建（单个/批量）
- ✅ 商品下架
- ✅ 价格更新  
- ✅ 标识更新
- ✅ 库存锁定
- ✅ 状态变更
- ✅ 商品删除
- ✅ 数据同步

### 4. 异常情况处理
- 库存不足的详细记录
- 并发更新失败的记录
- 数据不存在的记录
- 权限不足的记录

## 使用示例

### 查看商品创建日志
```bash
grep "PRODUCT_CREATE" goods-service.log | grep "listingId: KNET_12345"
```

### 查看价格变更历史
```bash
grep "PRICE_UPDATE" goods-service.log | grep "sku: AIR-JORDAN-1"
```

### 查看库存锁定情况
```bash
grep "INVENTORY_LOCK" goods-service.log | grep "2025-08-29"
```

### 查看操作人的所有操作
```bash
grep "操作人: api_user_001" goods-service.log
```

## 监控和告警建议

### 1. 关键指标监控
- 商品创建成功率
- 价格更新成功率  
- 库存锁定成功率
- 下架操作成功率

### 2. 异常告警
- 库存锁定失败频率过高
- 价格更新失败频率过高
- 并发更新冲突频率过高

### 3. 业务分析
- 每日商品创建数量趋势
- 价格变更频率分析
- 库存锁定模式分析
- 操作人行为分析

## 总结

通过本次改进，KnetProduct模型的变动日志已经形成了完善的追踪体系：

1. **完整性**: 覆盖了所有主要的数据变动场景
2. **详细性**: 记录了变更前后的完整信息对比
3. **可追踪性**: 每个操作都有明确的标识和时间戳
4. **可分析性**: 结构化的日志格式便于后续分析
5. **可监控性**: 支持基于日志的监控和告警

这套日志体系为商品数据的审计、问题排查、业务分析提供了强有力的支持。
