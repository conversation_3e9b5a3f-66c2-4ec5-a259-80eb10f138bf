# KnetProduct历史记录改造计划

## 1. 项目背景

### 1.1 需求概述
为了满足业务审计和数据追溯需求，需要对现有的KnetProduct商品模型新增历史变动记录功能。该功能需要记录所有KnetProduct数据的变动历史，包括创建、更新、删除等操作，同时确保历史记录的保存不影响主业务的正常进行。

### 1.2 设计原则
- **非侵入性**: 历史记录功能对现有业务代码影响最小
- **高性能**: 历史记录保存采用异步机制，不影响主业务性能
- **完整性**: 记录所有数据变动场景，确保审计追溯的完整性
- **可扩展性**: 设计支持未来其他实体的历史记录需求

## 2. 现状分析

### 2.1 KnetProduct模型分析

基于代码分析，KnetProduct模型具有以下特征：

**继承关系**:
- 继承自 `BaseEntity`，包含基础字段：`id`、`createTime`、`updateTime`、`delFlag`、`version`

**核心业务字段**:
```java
// 商品标识字段
private String listingId;     // knet_product唯一标识符
private String oneId;         // knetId
private String sku;           // sku
private String skuIndexed;    // sku检索索引
private String spec;          // 尺码

// 商品信息字段
private String brand;         // 品牌
private String remarks;       // 品名
private Long price;           // 价格（单位：美分）
private Integer stock;        // 库存数量
private String warehouse;     // 存储仓库

// 状态和标识字段
private ProductStatus status; // 商品状态 上架/下架
private ProductMark mark;     // 商品标识 hot sale, new, common

// 管理字段
private String creator;       // 商品创建人
private String source;        // 商品来源
```

### 2.2 数据变动场景分析

通过代码分析，识别出以下KnetProduct数据变动场景：

#### 2.2.1 创建操作
- **场景**: 通过API创建新商品
- **触发点**: `ApiGoodsProvider.createProducts()` -> `KnetProductServiceImpl.createByKnet()`
- **操作类型**: INSERT
- **影响字段**: 所有字段

#### 2.2.2 批量插入操作
- **场景**: 批量导入商品数据
- **触发点**: `KnetProductServiceImpl.insertIgnoreBatch()`
- **操作类型**: BATCH_INSERT
- **影响字段**: 所有字段

#### 2.2.3 下架操作
- **场景**: 商品下架
- **触发点**: `ApiGoodsProvider.offSale()` -> `KnetProductServiceImpl.updateKnetProductForOffSale()`
- **操作类型**: UPDATE
- **影响字段**: `status` (ON_SALE -> OFF_SALE)

#### 2.2.4 价格更新操作
- **场景**: 更新商品价格
- **触发点**: `ApiGoodsProvider.updatePrice()` -> `KnetProductServiceImpl.processKnetProductPrice()`
- **操作类型**: UPDATE
- **影响字段**: `price`

#### 2.2.5 商品标识更新操作
- **场景**: 批量更新商品标识
- **触发点**: `KnetProductServiceImpl.setProductModifyMark()`
- **操作类型**: UPDATE
- **影响字段**: `mark`

#### 2.2.6 商品信息同步更新操作
- **场景**: 从外部系统同步更新商品信息
- **触发点**: `KnetProductServiceImpl.updateKnetProductForSysSkuInfo()`
- **操作类型**: UPDATE
- **影响字段**: 多个字段（根据同步内容而定）

#### 2.2.7 库存锁定操作
- **场景**: 订单创建时锁定库存
- **触发点**: `KnetProductServiceImpl.lockInventory()`
- **操作类型**: UPDATE
- **影响字段**: `stock`

#### 2.2.8 逻辑删除操作
- **场景**: 软删除商品记录
- **触发点**: MyBatis-Plus自动处理
- **操作类型**: DELETE
- **影响字段**: `delFlag` (0 -> 1)

## 3. 设计方案

### 3.1 KnetProductHistory历史记录模型设计

```java
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "knet_product_history", description = "商品历史记录表")
@TableName("knet_product_history")
public class KnetProductHistory extends BaseEntity {
    
    // 关联原始记录
    @TableField("original_id")
    @Schema(description = "原始商品记录ID")
    private Long originalId;
    
    @TableField("listing_id")
    @Schema(description = "商品唯一标识符")
    private String listingId;
    
    // 操作信息
    @TableField("operation_type")
    @Schema(description = "操作类型")
    private HistoryOperationType operationType;
    
    @TableField("operation_time")
    @Schema(description = "操作时间")
    private Date operationTime;
    
    @TableField("operator")
    @Schema(description = "操作人")
    private String operator;
    
    @TableField("operation_source")
    @Schema(description = "操作来源")
    private String operationSource;
    
    // 变更前数据（JSON格式存储）
    @TableField("before_data")
    @Schema(description = "变更前数据")
    private String beforeData;
    
    // 变更后数据（JSON格式存储）
    @TableField("after_data")
    @Schema(description = "变更后数据")
    private String afterData;
    
    // 变更字段列表
    @TableField("changed_fields")
    @Schema(description = "变更字段列表，逗号分隔")
    private String changedFields;
    
    // 业务备注
    @TableField("remark")
    @Schema(description = "操作备注")
    private String remark;
}
```

### 3.2 操作类型枚举设计

```java
@Getter
@AllArgsConstructor
public enum HistoryOperationType {
    CREATE(1, "CREATE", "创建"),
    UPDATE(2, "UPDATE", "更新"),
    DELETE(3, "DELETE", "删除"),
    BATCH_INSERT(4, "BATCH_INSERT", "批量插入"),
    BATCH_UPDATE(5, "BATCH_UPDATE", "批量更新"),
    BATCH_DELETE(6, "BATCH_DELETE", "批量删除");
    
    @EnumValue
    @JsonValue
    private final Integer code;
    private final String name;
    private final String description;
}
```

## 4. 技术实现方案

### 4.1 异步历史记录保存机制

#### 4.1.1 消息队列设计
- **Exchange**: `product.history.exchange` (Topic类型)
- **Queue**: `product.history.save.queue`
- **Routing Key**: `product.history.save`

#### 4.1.2 消息体设计
```java
@Data
public class ProductHistoryMessage {
    private Long originalId;
    private String listingId;
    private HistoryOperationType operationType;
    private Date operationTime;
    private String operator;
    private String operationSource;
    private String beforeData;
    private String afterData;
    private List<String> changedFields;
    private String remark;
}
```

### 4.2 AOP切面拦截实现

#### 4.2.1 切面设计
```java
@Aspect
@Component
@Slf4j
public class ProductHistoryAspect {
    
    @Autowired
    private ProductHistoryMessageSender messageSender;
    
    // 拦截Service层的增删改操作
    @Around("execution(* com.knet.goods.service.impl.KnetProductServiceImpl.save*(..)) || " +
            "execution(* com.knet.goods.service.impl.KnetProductServiceImpl.update*(..)) || " +
            "execution(* com.knet.goods.service.impl.KnetProductServiceImpl.remove*(..)) || " +
            "execution(* com.knet.goods.service.impl.KnetProductServiceImpl.insert*(..))")
    public Object recordHistory(ProceedingJoinPoint joinPoint) throws Throwable {
        // 实现历史记录逻辑
    }
}
```

### 4.3 历史记录服务实现

#### 4.3.1 服务接口设计
```java
public interface IKnetProductHistoryService extends IService<KnetProductHistory> {
    
    /**
     * 保存历史记录
     */
    void saveHistory(ProductHistoryMessage message);
    
    /**
     * 查询商品历史记录
     */
    IPage<KnetProductHistory> queryHistory(ProductHistoryQueryRequest request);
    
    /**
     * 查询特定操作的历史记录
     */
    List<KnetProductHistory> queryByOperation(String listingId, HistoryOperationType operationType);
}
```

## 5. 数据库设计

### 5.1 表结构设计

#### 5.1.1 knet_product_history表结构

```sql
CREATE TABLE `knet_product_history` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',

    -- 关联信息
    `original_id` BIGINT NOT NULL COMMENT '原始商品记录ID',
    `listing_id` VARCHAR(100) NOT NULL COMMENT '商品唯一标识符',

    -- 操作信息
    `operation_type` VARCHAR(20) NOT NULL COMMENT '操作类型：CREATE/UPDATE/DELETE/BATCH_INSERT/BATCH_UPDATE/BATCH_DELETE',
    `operation_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `operator` VARCHAR(100) DEFAULT NULL COMMENT '操作人',
    `operation_source` VARCHAR(100) DEFAULT NULL COMMENT '操作来源',

    -- 数据变更信息
    `before_data` JSON DEFAULT NULL COMMENT '变更前数据（JSON格式）',
    `after_data` JSON DEFAULT NULL COMMENT '变更后数据（JSON格式）',
    `changed_fields` TEXT DEFAULT NULL COMMENT '变更字段列表，逗号分隔',

    -- 业务信息
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '操作备注',

    -- 基础字段
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `del_flag` TINYINT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '版本号',

    PRIMARY KEY (`id`),
    INDEX `idx_original_id` (`original_id`),
    INDEX `idx_listing_id` (`listing_id`),
    INDEX `idx_operation_type` (`operation_type`),
    INDEX `idx_operation_time` (`operation_time`),
    INDEX `idx_operator` (`operator`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_composite_query` (`listing_id`, `operation_type`, `operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品历史记录表';
```

#### 5.1.2 索引设计说明

1. **主键索引**: `id` - 保证记录唯一性
2. **业务索引**:
   - `idx_original_id` - 根据原始商品ID查询历史
   - `idx_listing_id` - 根据商品标识查询历史
   - `idx_operation_type` - 根据操作类型查询
3. **时间索引**:
   - `idx_operation_time` - 根据操作时间范围查询
   - `idx_create_time` - 根据记录创建时间查询
4. **复合索引**:
   - `idx_composite_query` - 支持常见的组合查询场景

#### 5.1.3 JSON字段设计

**before_data和after_data字段存储格式**:
```json
{
    "listingId": "KNET_12345",
    "oneId": "ONE_67890",
    "sku": "AIR-JORDAN-1",
    "spec": "US10",
    "brand": "Nike",
    "remarks": "Air Jordan 1 Retro High",
    "price": 15000,
    "stock": 5,
    "warehouse": "US_WEST",
    "status": "ON_SALE",
    "mark": "HOT_SALE",
    "creator": "api_user_001",
    "source": "external_api"
}
```

### 5.2 数据归档策略

#### 5.2.1 分区表设计（可选）
```sql
-- 按月分区的历史表（适用于大数据量场景）
CREATE TABLE `knet_product_history_partitioned` (
    -- 字段定义同上
    ...
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (YEAR(create_time) * 100 + MONTH(create_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    -- 继续添加分区...
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

#### 5.2.2 归档表设计
```sql
-- 历史数据归档表
CREATE TABLE `knet_product_history_archive` (
    -- 结构与主表相同
    ...
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品历史记录归档表';
```

## 6. 详细实现方案

### 6.1 模型类实现

#### 6.1.1 KnetProductHistory实体类
```java
package com.knet.goods.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.goods.model.enums.HistoryOperationType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "knet_product_history", description = "商品历史记录表")
@TableName("knet_product_history")
public class KnetProductHistory extends BaseEntity {

    @TableField("original_id")
    @Schema(description = "原始商品记录ID")
    private Long originalId;

    @TableField("listing_id")
    @Schema(description = "商品唯一标识符")
    private String listingId;

    @TableField("operation_type")
    @Schema(description = "操作类型")
    private HistoryOperationType operationType;

    @TableField("operation_time")
    @Schema(description = "操作时间")
    private Date operationTime;

    @TableField("operator")
    @Schema(description = "操作人")
    private String operator;

    @TableField("operation_source")
    @Schema(description = "操作来源")
    private String operationSource;

    @TableField("before_data")
    @Schema(description = "变更前数据")
    private String beforeData;

    @TableField("after_data")
    @Schema(description = "变更后数据")
    private String afterData;

    @TableField("changed_fields")
    @Schema(description = "变更字段列表，逗号分隔")
    private String changedFields;

    @TableField("remark")
    @Schema(description = "操作备注")
    private String remark;
}
```

#### 6.1.2 操作类型枚举
```java
package com.knet.goods.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HistoryOperationType {
    CREATE(1, "CREATE", "创建"),
    UPDATE(2, "UPDATE", "更新"),
    DELETE(3, "DELETE", "删除"),
    BATCH_INSERT(4, "BATCH_INSERT", "批量插入"),
    BATCH_UPDATE(5, "BATCH_UPDATE", "批量更新"),
    BATCH_DELETE(6, "BATCH_DELETE", "批量删除");

    private final Integer code;

    @EnumValue
    @JsonValue
    private final String name;

    private final String description;

    public static HistoryOperationType fromName(String name) {
        for (HistoryOperationType type : values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown HistoryOperationType: " + name);
    }
}
```

### 6.2 消息机制实现

#### 6.2.1 历史记录消息体
```java
package com.knet.goods.model.dto.message;

import com.knet.goods.model.enums.HistoryOperationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductHistoryMessage {
    private Long originalId;
    private String listingId;
    private HistoryOperationType operationType;
    private Date operationTime;
    private String operator;
    private String operationSource;
    private String beforeData;
    private String afterData;
    private List<String> changedFields;
    private String remark;
}
```

#### 6.2.2 消息发送器
```java
package com.knet.goods.service.message;

import com.knet.goods.model.dto.message.ProductHistoryMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ProductHistoryMessageSender {

    private static final String EXCHANGE = "product.history.exchange";
    private static final String ROUTING_KEY = "product.history.save";

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void sendHistoryMessage(ProductHistoryMessage message) {
        try {
            rabbitTemplate.convertAndSend(EXCHANGE, ROUTING_KEY, message);
            log.info("发送商品历史记录消息成功: listingId={}, operationType={}",
                    message.getListingId(), message.getOperationType());
        } catch (Exception e) {
            log.error("发送商品历史记录消息失败: listingId={}, operationType={}",
                    message.getListingId(), message.getOperationType(), e);
            // 这里可以实现消息重试机制或保存到本地重试表
        }
    }
}
```

#### 6.2.3 消息消费者
```java
package com.knet.goods.listener;

import com.knet.goods.model.dto.message.ProductHistoryMessage;
import com.knet.goods.service.IKnetProductHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ProductHistoryMessageConsumer {

    @Autowired
    private IKnetProductHistoryService historyService;

    @RabbitListener(queues = "product.history.save.queue")
    public void handleHistoryMessage(ProductHistoryMessage message) {
        try {
            log.info("接收到商品历史记录消息: listingId={}, operationType={}",
                    message.getListingId(), message.getOperationType());

            historyService.saveHistory(message);

            log.info("处理商品历史记录消息成功: listingId={}", message.getListingId());
        } catch (Exception e) {
            log.error("处理商品历史记录消息失败: listingId={}", message.getListingId(), e);
            throw e; // 重新抛出异常，触发消息重试
        }
    }
}
```

### 6.3 AOP切面实现

#### 6.3.1 历史记录切面
```java
package com.knet.goods.aspect;

import com.alibaba.fastjson2.JSON;
import com.knet.common.context.ApiKeyContext;
import com.knet.goods.model.dto.message.ProductHistoryMessage;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.model.enums.HistoryOperationType;
import com.knet.goods.service.message.ProductHistoryMessageSender;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Aspect
@Component
@Slf4j
public class ProductHistoryAspect {

    @Autowired
    private ProductHistoryMessageSender messageSender;

    @Around("execution(* com.knet.goods.service.impl.KnetProductServiceImpl.save*(..)) || " +
            "execution(* com.knet.goods.service.impl.KnetProductServiceImpl.update*(..)) || " +
            "execution(* com.knet.goods.service.impl.KnetProductServiceImpl.remove*(..)) || " +
            "execution(* com.knet.goods.service.impl.KnetProductServiceImpl.insert*(..))")
    public Object recordHistory(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();

        // 执行原方法
        Object result = joinPoint.proceed();

        // 异步发送历史记录消息
        try {
            sendHistoryMessage(methodName, args, result);
        } catch (Exception e) {
            log.error("发送历史记录消息失败", e);
            // 不影响主业务，只记录日志
        }

        return result;
    }

    private void sendHistoryMessage(String methodName, Object[] args, Object result) {
        // 根据方法名和参数构建历史记录消息
        HistoryOperationType operationType = determineOperationType(methodName);

        if (operationType != null) {
            // 构建并发送消息
            ProductHistoryMessage message = buildHistoryMessage(operationType, args, result);
            if (message != null) {
                messageSender.sendHistoryMessage(message);
            }
        }
    }

    private HistoryOperationType determineOperationType(String methodName) {
        if (methodName.startsWith("save") || methodName.startsWith("create")) {
            return HistoryOperationType.CREATE;
        } else if (methodName.startsWith("update")) {
            return HistoryOperationType.UPDATE;
        } else if (methodName.startsWith("remove") || methodName.startsWith("delete")) {
            return HistoryOperationType.DELETE;
        } else if (methodName.startsWith("insert") && methodName.contains("Batch")) {
            return HistoryOperationType.BATCH_INSERT;
        }
        return null;
    }

    private ProductHistoryMessage buildHistoryMessage(HistoryOperationType operationType,
                                                    Object[] args, Object result) {
        // 根据操作类型和参数构建消息
        // 具体实现根据不同的方法签名进行适配
        return ProductHistoryMessage.builder()
                .operationType(operationType)
                .operationTime(new Date())
                .operator(ApiKeyContext.getApiKey())
                .operationSource("service_layer")
                .build();
    }
}
```

## 7. 实施计划

### 7.1 开发阶段
1. **模型和枚举创建** (1天)
   - 创建KnetProductHistory实体类
   - 创建HistoryOperationType枚举
   - 创建相关DTO类

2. **数据库表创建** (0.5天)
   - 执行建表SQL
   - 创建必要的索引
   - 配置分区策略（如需要）

3. **消息队列机制实现** (2天)
   - 配置RabbitMQ交换机和队列
   - 实现消息发送器
   - 实现消息消费者
   - 添加消息重试机制

4. **AOP切面实现** (2天)
   - 实现ProductHistoryAspect切面
   - 适配不同的Service方法
   - 实现数据变更检测逻辑

5. **历史记录服务实现** (2天)
   - 实现IKnetProductHistoryService接口
   - 实现Mapper和XML配置
   - 实现数据转换和保存逻辑

6. **查询接口开发** (1天)
   - 实现历史记录查询Controller
   - 实现分页查询功能
   - 实现按条件筛选功能

### 7.2 测试阶段
1. **单元测试** (2天)
   - AOP切面测试
   - 服务层测试
   - 消息机制测试

2. **集成测试** (2天)
   - 端到端流程测试
   - 消息队列集成测试
   - 数据库操作测试

3. **性能测试** (1天)
   - 历史记录保存性能测试
   - 查询接口性能测试
   - 消息队列性能测试

### 7.3 部署阶段
1. **配置更新** (0.5天)
   - 更新RabbitMQ配置
   - 更新应用配置文件

2. **生产部署** (1天)
   - 代码部署
   - 功能验证
   - 监控配置

## 8. 完整MySQL建表SQL

### 8.1 主表创建SQL

```sql
-- =====================================================
-- KnetProduct历史记录表
-- 基于MySQL 8.0设计，支持JSON字段和完整索引
-- =====================================================

CREATE TABLE `knet_product_history` (
    -- 主键
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',

    -- 关联信息
    `original_id` BIGINT NOT NULL COMMENT '原始商品记录ID，关联knet_product.id',
    `listing_id` VARCHAR(100) NOT NULL COMMENT '商品唯一标识符，冗余存储便于查询',

    -- 操作信息
    `operation_type` VARCHAR(20) NOT NULL COMMENT '操作类型：CREATE/UPDATE/DELETE/BATCH_INSERT/BATCH_UPDATE/BATCH_DELETE',
    `operation_time` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '操作时间，精确到毫秒',
    `operator` VARCHAR(100) DEFAULT NULL COMMENT '操作人，可以是用户ID或API Key',
    `operation_source` VARCHAR(100) DEFAULT NULL COMMENT '操作来源：api/admin/system/sync等',

    -- 数据变更信息（使用JSON类型存储，便于查询和分析）
    `before_data` JSON DEFAULT NULL COMMENT '变更前数据，完整的商品信息JSON',
    `after_data` JSON DEFAULT NULL COMMENT '变更后数据，完整的商品信息JSON',
    `changed_fields` TEXT DEFAULT NULL COMMENT '变更字段列表，逗号分隔，如：price,status,stock',

    -- 业务信息
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '操作备注，记录业务相关信息',

    -- 基础字段（继承自BaseEntity）
    `create_time` TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    `update_time` TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录更新时间',
    `del_flag` TINYINT DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    `version` INT DEFAULT 1 COMMENT '版本号，用于乐观锁',

    -- 主键约束
    PRIMARY KEY (`id`),

    -- 业务索引
    INDEX `idx_original_id` (`original_id`) COMMENT '原始商品ID索引',
    INDEX `idx_listing_id` (`listing_id`) COMMENT '商品标识索引',
    INDEX `idx_operation_type` (`operation_type`) COMMENT '操作类型索引',
    INDEX `idx_operation_time` (`operation_time`) COMMENT '操作时间索引',
    INDEX `idx_operator` (`operator`) COMMENT '操作人索引',
    INDEX `idx_create_time` (`create_time`) COMMENT '创建时间索引',

    -- 复合索引（支持常见查询场景）
    INDEX `idx_listing_operation` (`listing_id`, `operation_type`, `operation_time`) COMMENT '商品操作复合索引',
    INDEX `idx_time_range` (`operation_time`, `operation_type`) COMMENT '时间范围查询索引',
    INDEX `idx_operator_time` (`operator`, `operation_time`) COMMENT '操作人时间索引',

    -- JSON字段索引（MySQL 8.0支持）
    INDEX `idx_before_price` ((CAST(`before_data`->>'$.price' AS UNSIGNED))) COMMENT '变更前价格索引',
    INDEX `idx_after_price` ((CAST(`after_data`->>'$.price' AS UNSIGNED))) COMMENT '变更后价格索引',
    INDEX `idx_before_status` ((CAST(`before_data`->>'$.status' AS CHAR(20)))) COMMENT '变更前状态索引',
    INDEX `idx_after_status` ((CAST(`after_data`->>'$.status' AS CHAR(20)))) COMMENT '变更后状态索引'

) ENGINE=InnoDB
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci
  COMMENT='商品历史记录表 - 记录KnetProduct的所有变更历史'
  ROW_FORMAT=DYNAMIC;
```

### 8.2 分区表创建SQL（可选）

```sql
-- =====================================================
-- 分区版本的历史记录表（按月分区）
-- 适用于历史数据量较大的场景
-- =====================================================

CREATE TABLE `knet_product_history_partitioned` (
    -- 字段定义与主表相同
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `original_id` BIGINT NOT NULL COMMENT '原始商品记录ID',
    `listing_id` VARCHAR(100) NOT NULL COMMENT '商品唯一标识符',
    `operation_type` VARCHAR(20) NOT NULL COMMENT '操作类型',
    `operation_time` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '操作时间',
    `operator` VARCHAR(100) DEFAULT NULL COMMENT '操作人',
    `operation_source` VARCHAR(100) DEFAULT NULL COMMENT '操作来源',
    `before_data` JSON DEFAULT NULL COMMENT '变更前数据',
    `after_data` JSON DEFAULT NULL COMMENT '变更后数据',
    `changed_fields` TEXT DEFAULT NULL COMMENT '变更字段列表',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '操作备注',
    `create_time` TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    `update_time` TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录更新时间',
    `del_flag` TINYINT DEFAULT 0 COMMENT '删除标志',
    `version` INT DEFAULT 1 COMMENT '版本号',

    PRIMARY KEY (`id`, `create_time`),
    INDEX `idx_original_id` (`original_id`),
    INDEX `idx_listing_id` (`listing_id`),
    INDEX `idx_operation_type` (`operation_type`),
    INDEX `idx_operation_time` (`operation_time`),
    INDEX `idx_listing_operation` (`listing_id`, `operation_type`, `operation_time`)

) ENGINE=InnoDB
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci
  COMMENT='商品历史记录分区表'
  ROW_FORMAT=DYNAMIC
  PARTITION BY RANGE (YEAR(`create_time`) * 100 + MONTH(`create_time`)) (
    PARTITION p202501 VALUES LESS THAN (202502) COMMENT '2025年1月',
    PARTITION p202502 VALUES LESS THAN (202503) COMMENT '2025年2月',
    PARTITION p202503 VALUES LESS THAN (202504) COMMENT '2025年3月',
    PARTITION p202504 VALUES LESS THAN (202505) COMMENT '2025年4月',
    PARTITION p202505 VALUES LESS THAN (202506) COMMENT '2025年5月',
    PARTITION p202506 VALUES LESS THAN (202507) COMMENT '2025年6月',
    PARTITION p202507 VALUES LESS THAN (202508) COMMENT '2025年7月',
    PARTITION p202508 VALUES LESS THAN (202509) COMMENT '2025年8月',
    PARTITION p202509 VALUES LESS THAN (202510) COMMENT '2025年9月',
    PARTITION p202510 VALUES LESS THAN (202511) COMMENT '2025年10月',
    PARTITION p202511 VALUES LESS THAN (202512) COMMENT '2025年11月',
    PARTITION p202512 VALUES LESS THAN (202513) COMMENT '2025年12月',
    PARTITION p_future VALUES LESS THAN MAXVALUE COMMENT '未来分区'
);
```

## 9. 风险评估与应对

### 9.1 性能风险
- **风险**: 历史记录可能影响主业务性能
- **应对**: 采用异步消息机制，确保主业务不受影响

### 9.2 存储风险
- **风险**: 历史数据量快速增长
- **应对**: 实施数据归档策略，定期清理过期历史数据

### 9.3 一致性风险
- **风险**: 异步机制可能导致历史记录丢失
- **应对**: 实施消息重试机制和死信队列处理

## 10. 监控与运维

### 10.1 监控指标
- 历史记录保存成功率
- 消息队列积压情况
- 历史记录查询性能

### 10.2 告警机制
- 消息发送失败告警
- 队列积压告警
- 数据库性能告警

---

## 总结

本改造计划提供了完整的KnetProduct历史记录功能实现方案，包括：

1. **完整的模型设计**: KnetProductHistory实体类和相关枚举
2. **异步消息机制**: 基于RabbitMQ的消息发送和消费
3. **AOP切面拦截**: 自动捕获数据变动操作
4. **完整的数据库设计**: 包含主表、分区表、归档表和相关索引
5. **详细的实施计划**: 分阶段的开发、测试和部署计划

该方案确保了历史记录功能的非侵入性和高性能，同时提供了完整的数据追溯能力。

---

*本文档将在后续章节补充完整的MySQL建表SQL和详细的实现代码。*
