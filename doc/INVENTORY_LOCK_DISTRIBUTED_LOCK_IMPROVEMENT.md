# 库存锁定分布式锁改进文档

## 问题背景

在多实例并发场景下，订单库存锁定任务存在数据不一致问题：

1. **竞态条件**：查询可锁定商品和执行锁定操作之间存在时间窗口
2. **超卖风险**：多个实例同时查询到相同商品并尝试锁定
3. **状态检查不严格**：更新时未再次确认商品状态为ON_SALE

## 解决方案

### 1. 添加分布式锁保护

#### 主要库存锁定方法 - DefaultInventoryLockStrategy.lockSingleItem

**文件位置**: `goods-services/src/main/java/com/knet/goods/service/impl/DefaultInventoryLockStrategy.java`

**改进内容**:
```java
@DistributedLock(key = "'inventory:lock:' + #item.sku + ':' + #item.size + ':' + #item.price", expire = 30)
public InventoryLockSuccessMessage.LockedProductInfo lockSingleItem(SubOrderItemResp item, String account) {
    // 库存锁定逻辑
}
```

**锁定策略**:
- 锁定粒度：SKU + 尺码 + 价格
- 过期时间：30秒
- 确保同一商品规格的库存锁定操作串行执行

#### 兼容性保护 - KnetProductServiceImpl.lockInventory

**文件位置**: `goods-services/src/main/java/com/knet/goods/service/impl/KnetProductServiceImpl.java`

**改进内容**:
```java
@DistributedLock(key = "'inventory:legacy:lock:' + #request.hashCode()", expire = 30)
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public boolean lockInventory(CheckAndLockInvRequest request) {
    // 旧版库存锁定逻辑
}
```

**锁定策略**:
- 锁定粒度：整个请求
- 过期时间：30秒
- 为旧版本方法提供兼容性保护

### 2. 增强状态检查

#### 双重状态验证

**查询阶段**:
```java
queryWrapper
    .eq(KnetProduct::getSku, item.getSku())
    .eq(KnetProduct::getPrice, originalPriceCents)
    .eq(KnetProduct::getSpec, item.getSize())
    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)  // 第一次状态检查
```

**更新阶段**:
```java
updateWrapper
    .in(KnetProduct::getId, idsToLock)
    .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)  // 第二次状态检查
    .set(KnetProduct::getStatus, ProductStatus.LOCKED);
```

#### 更新结果验证

```java
int updatedCount = knetProductMapper.update(null, updateWrapper);
if (updatedCount != idsToLock.size()) {
    log.error("商品锁定失败，期望锁定{}个，实际锁定{}个。可能原因：商品状态已变更或被其他实例锁定", 
            idsToLock.size(), updatedCount);
    throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 锁定失败，商品状态已变更");
}
```

## 技术实现细节

### 分布式锁机制

**基础实现**: 使用Redis SETNX命令实现分布式锁
**锁释放**: 使用Lua脚本确保原子性释放
**超时保护**: 设置锁过期时间防止死锁

### 锁定粒度设计

1. **细粒度锁定** (DefaultInventoryLockStrategy):
   - 锁定键: `inventory:lock:{sku}:{size}:{price}`
   - 优势: 不同商品规格可并行处理
   - 适用: 高并发场景

2. **粗粒度锁定** (KnetProductServiceImpl):
   - 锁定键: `inventory:legacy:lock:{requestHash}`
   - 优势: 实现简单，兼容性好
   - 适用: 兼容旧版本调用

### 错误处理增强

**详细错误日志**:
```java
log.error("商品锁定失败，期望锁定{}个，实际锁定{}个。可能原因：商品状态已变更或被其他实例锁定", 
        idsToLock.size(), updatedCount);
```

**明确异常信息**:
```java
throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 锁定失败，商品状态已变更");
```

## 调用链路分析

### 主要调用路径

```
OrderMessageConsumer.processOrder()
    ↓
InventoryServiceImpl.checkAndLockInventoryWithUserId()
    ↓
DefaultInventoryLockStrategy.lockInventoryWithDetails()
    ↓
DefaultInventoryLockStrategy.lockSingleItem() [已加分布式锁]
```

### 兼容性调用路径

```
InventoryServiceImpl.checkAndLockInventory()
    ↓
KnetProductServiceImpl.lockInventory() [已加分布式锁]
```

## 性能影响评估

### 正面影响
- 消除超卖风险
- 确保数据一致性
- 提高系统可靠性

### 潜在影响
- 增加锁竞争开销（约1-3ms）
- 降低并发处理能力（相同商品规格）
- Redis连接数增加

### 优化建议
- 监控锁竞争情况
- 根据业务需求调整锁过期时间
- 考虑使用更细粒度的锁定策略

## 测试建议

### 并发测试
1. 模拟多实例同时锁定相同商品
2. 验证库存数量准确性
3. 测试锁超时场景

### 性能测试
1. 对比加锁前后的响应时间
2. 测试高并发场景下的吞吐量
3. 监控Redis性能指标

### 异常测试
1. Redis连接异常场景
2. 锁获取失败处理
3. 商品状态变更场景

## 监控指标

### 业务指标
- 库存锁定成功率
- 库存锁定失败原因分布
- 超卖事件数量

### 技术指标
- 分布式锁获取耗时
- 分布式锁竞争次数
- Redis连接池使用情况

## 总结

通过添加分布式锁保护和增强状态检查，有效解决了多实例并发场景下的库存锁定数据不一致问题：

1. **消除竞态条件**: 分布式锁确保库存锁定操作的原子性
2. **防止超卖**: 双重状态检查和更新结果验证
3. **提高可靠性**: 详细的错误处理和日志记录
4. **保持兼容性**: 同时保护新旧两套库存锁定实现

该改进确保了在多实例部署环境下，库存锁定操作的数据一致性和业务正确性。
