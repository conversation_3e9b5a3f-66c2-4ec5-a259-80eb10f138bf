# 订单退款防护机制增强文档

## 背景问题

原有的退款实现存在以下潜在风险：
1. **缺乏重复退款检查**：没有验证同一订单项是否已经退款
2. **累计退款金额校验不完善**：只检查单次退款，未校验总累计退款金额
3. **并发退款风险**：虽然有分布式锁，但防护机制不够全面
4. **消息队列重复消费**：MQ消息可能重复投递导致重复退款

## 解决方案

### 1. 增强退款记录查询服务

新增 `ISysRefundRecordService` 接口方法：

```java
/**
 * 根据订单项ID查询退款记录
 */
List<SysRefundRecord> getRefundRecordsByOrderItemId(Long orderItemId);

/**
 * 计算订单项的累计退款金额
 */
BigDecimal getTotalRefundAmountByOrderItemId(Long orderItemId);

/**
 * 检查订单项是否已经退款
 */
boolean isOrderItemRefunded(Long orderItemId);
```

### 2. 多重防护机制

#### 2.1 分布式锁防护
```java
@DistributedLock(key = "'payment:refund:'+#request.userId+':'+#request.orderItemId", expire = 10)
```
- 基于用户ID和订单项ID的组合锁
- 锁定时间延长至10秒，确保退款操作完整性

#### 2.2 重复退款检查
```java
// 1. 检查订单项是否已经退款
if (sysRefundRecordService.isOrderItemRefunded(request.getOrderItemId())) {
    log.warn("订单项已退款，不能重复退款: orderItemId={}", request.getOrderItemId());
    throw new ServiceException("订单项已退款，不能重复退款");
}
```

#### 2.3 支付状态验证
```java
// 2. 订单支付完成可以退款，未支付订单不退款
if (!KnetPaymentGroupStatus.COMPLETED.equals(paymentGroup.getStatus())) {
    throw new ServiceException("订单未支付完成，无法退款");
}
```

#### 2.4 累计退款金额校验
```java
// 3. 检查累计退款金额是否超过已支付金额
BigDecimal totalRefundAmount = sysRefundRecordService.getTotalRefundAmountByOrderItemId(request.getOrderItemId());
BigDecimal newTotalRefundAmount = totalRefundAmount.add(request.getAmount());

if (newTotalRefundAmount.compareTo(paymentGroup.getPaidAmount()) > 0) {
    throw new ServiceException("累计退款金额不能大于已支付金额");
}
```

#### 2.5 单次退款金额校验
```java
// 4. 单次退款金额不能大于已支付金额
if (request.getAmount().compareTo(paymentGroup.getPaidAmount()) > 0) {
    throw new ServiceException("单次退款金额不能大于已支付金额");
}
```

#### 2.6 退款金额有效性校验
```java
// 5. 退款金额必须大于0
if (request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
    throw new ServiceException("退款金额必须大于0");
}
```

### 3. 数据库层面的防护

#### 3.1 退款记录查询优化
- 查询条件：`itemId = orderItemId AND status = 1`（只查询退款成功的记录）
- 使用MyBatis-Plus的LambdaQueryWrapper确保类型安全

#### 3.2 事务保证
- 整个退款流程使用 `@Transactional(rollbackFor = Exception.class)`
- 确保退款记录创建、钱包余额增加、钱包记录创建的原子性

### 4. MQ消息处理防护

现有的消息队列处理已经包含：
- **幂等性检查**：使用Redis缓存检查消息是否已处理
- **手动ACK**：确保消息处理成功后才确认
- **异常重试**：处理失败时拒绝消息并重新入队

```java
if (!RedisCacheUtil.setIfAbsent(PAYMENT_ORDER_PROCESSED.formatted(messageId), PROCESSED, 60)) {
    log.warn("payment服务重复消息: {}", messageId);
    channel.basicAck(deliveryTag, false);
    return;
}
```

## 防护机制流程图

```mermaid
flowchart TD
    A[退款请求] --> B[分布式锁获取]
    B --> C{获取锁成功?}
    C -->|否| D[等待/失败]
    C -->|是| E[检查订单项是否已退款]
    E --> F{已退款?}
    F -->|是| G[抛出重复退款异常]
    F -->|否| H[检查支付订单状态]
    H --> I{支付完成?}
    I -->|否| J[抛出未支付异常]
    I -->|是| K[计算累计退款金额]
    K --> L{累计金额 > 已支付金额?}
    L -->|是| M[抛出金额超限异常]
    L -->|否| N[检查单次退款金额]
    N --> O{单次金额 > 已支付金额?}
    O -->|是| P[抛出金额超限异常]
    O -->|否| Q[检查退款金额有效性]
    Q --> R{金额 > 0?}
    R -->|否| S[抛出无效金额异常]
    R -->|是| T[开始事务处理]
    T --> U[创建退款记录]
    U --> V[增加钱包余额]
    V --> W[创建钱包记录]
    W --> X[提交事务]
    X --> Y[释放分布式锁]
    Y --> Z[退款成功]
```

## 关键改进点

### 1. 防护层级
- **应用层**：分布式锁 + 业务逻辑校验
- **服务层**：退款记录查重 + 金额校验
- **数据层**：事务保证 + 数据一致性
- **消息层**：幂等性检查 + 异常处理

### 2. 性能优化
- 退款记录查询使用索引字段（itemId, status）
- 分布式锁时间合理设置（10秒）
- 异常情况快速失败，减少资源占用

### 3. 日志完善
- 关键节点增加详细日志记录
- 异常情况记录完整上下文信息
- 便于问题排查和监控告警

## 测试建议

### 1. 单元测试
- 测试各种边界条件和异常场景
- 验证金额计算的精度和正确性
- 模拟并发请求测试分布式锁效果

### 2. 集成测试
- 测试MQ消息重复投递场景
- 验证事务回滚机制
- 测试高并发退款请求

### 3. 压力测试
- 模拟大量并发退款请求
- 验证系统在高负载下的稳定性
- 监控数据库连接池和Redis性能

## 监控建议

### 1. 业务监控
- 退款成功率监控
- 退款处理时间监控
- 重复退款拦截次数统计

### 2. 技术监控
- 分布式锁获取成功率
- 数据库查询性能
- MQ消息处理延迟

### 3. 告警机制
- 退款失败率超过阈值告警
- 分布式锁获取失败告警
- 异常退款金额告警

## 总结

通过本次改进，我们建立了完善的多层次退款防护机制：

1. **彻底解决重复退款问题**：通过退款记录查重和分布式锁双重保护
2. **严格的金额校验**：累计退款金额、单次退款金额、有效性三重检查
3. **完整的事务保证**：确保退款操作的原子性和一致性
4. **健壮的异常处理**：各种异常场景都有相应的处理机制
5. **完善的日志记录**：便于问题排查和系统监控

这套防护机制能够有效防范同一订单多次退款的风险，保证支付系统的资金安全。
